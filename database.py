#!/usr/bin/env python3
"""
قاعدة البيانات - Mürşid Database
إعداد وإدارة قاعدة البيانات للتطبيق
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات"""

    def __init__(self, db_path: str = "mursid.db"):
        """
        تهيئة مدير قاعدة البيانات

        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """
        الحصول على اتصال بقاعدة البيانات

        Returns:
            sqlite3.Connection: اتصال قاعدة البيانات
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn

    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # إنشاء جدول المستخدمين
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        first_name TEXT NOT NULL,
                        last_name TEXT NOT NULL,
                        kimlik_number TEXT UNIQUE NOT NULL,
                        phone_number TEXT NOT NULL,
                        birth_date TEXT NOT NULL,
                        address TEXT NOT NULL,
                        email TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        is_admin BOOLEAN DEFAULT FALSE,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP NULL
                    )
                ''')

                # إنشاء جدول جلسات المستخدمين
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        session_token TEXT UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        ip_address TEXT,
                        user_agent TEXT,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                    )
                ''')

                # إنشاء جدول سجلات النظام
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        action TEXT NOT NULL,
                        details TEXT,
                        ip_address TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
                    )
                ''')

                # إنشاء جدول إعدادات النظام
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        setting_key TEXT UNIQUE NOT NULL,
                        setting_value TEXT NOT NULL,
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # إنشاء جدول محاولات تسجيل الدخول الفاشلة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS failed_login_attempts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL,
                        ip_address TEXT,
                        attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        reason TEXT
                    )
                ''')

                # إنشاء جدول البلاغات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS reports (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        title TEXT NOT NULL,
                        description TEXT NOT NULL,
                        category TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        priority TEXT DEFAULT 'medium',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        resolved_at TIMESTAMP NULL,
                        assigned_to INTEGER,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
                        FOREIGN KEY (assigned_to) REFERENCES users (id) ON DELETE SET NULL
                    )
                ''')

                # إنشاء جدول الغرامات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fines (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        fine_type TEXT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        description TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        issued_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        due_date TIMESTAMP NOT NULL,
                        paid_date TIMESTAMP NULL,
                        issued_by INTEGER,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL,
                        FOREIGN KEY (issued_by) REFERENCES users (id) ON DELETE SET NULL
                    )
                ''')

                # إنشاء فهارس لتحسين الأداء
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_kimlik ON users(kimlik_number)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_logs_user ON system_logs(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON system_logs(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_reports_user ON reports(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_fines_user ON fines(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_fines_status ON fines(status)')

                conn.commit()
                logger.info("تم إنشاء قاعدة البيانات والجداول بنجاح")

                # إنشاء المستخدم الإداري الافتراضي
                self._create_default_admin()

                # إنشاء الإعدادات الافتراضية
                self._create_default_settings()

                # إنشاء بيانات وهمية للعرض
                self._create_sample_data()

        except Exception as e:
            logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
            raise

    def _create_default_admin(self):
        """إنشاء المستخدم الإداري الافتراضي"""
        try:
            from security import SecurityManager
            security = SecurityManager()

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # التحقق من وجود المستخدم الإداري
                cursor.execute('SELECT id FROM users WHERE email = ?', ('<EMAIL>',))
                if cursor.fetchone():
                    return  # المستخدم الإداري موجود بالفعل

                # إنشاء المستخدم الإداري
                password_hash = security.hash_password('admin123')
                cursor.execute('''
                    INSERT INTO users (
                        first_name, last_name, kimlik_number, phone_number,
                        birth_date, address, email, password_hash, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    'Admin', 'User', '12345678901', '+90 ************',
                    '1990-01-01', 'Admin Address', '<EMAIL>',
                    password_hash, True
                ))

                conn.commit()
                logger.info("تم إنشاء المستخدم الإداري الافتراضي")

        except Exception as e:
            logger.error(f"خطأ في إنشاء المستخدم الإداري: {e}")

    def _create_default_settings(self):
        """إنشاء الإعدادات الافتراضية للنظام"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                default_settings = [
                    ('app_name', 'Mürşid', 'اسم التطبيق'),
                    ('app_version', '1.0.0', 'إصدار التطبيق'),
                    ('max_login_attempts', '5', 'عدد محاولات تسجيل الدخول المسموحة'),
                    ('session_timeout', '3600', 'مدة انتهاء الجلسة بالثواني'),
                    ('password_min_length', '8', 'الحد الأدنى لطول كلمة المرور'),
                    ('require_email_verification', 'false', 'طلب تأكيد البريد الإلكتروني'),
                    ('default_language', 'tr', 'اللغة الافتراضية'),
                    ('enable_registration', 'true', 'تفعيل التسجيل الجديد'),
                    ('maintenance_mode', 'false', 'وضع الصيانة'),
                ]

                for key, value, description in default_settings:
                    cursor.execute('''
                        INSERT OR IGNORE INTO system_settings
                        (setting_key, setting_value, description)
                        VALUES (?, ?, ?)
                    ''', (key, value, description))

                conn.commit()
                logger.info("تم إنشاء الإعدادات الافتراضية")

        except Exception as e:
            logger.error(f"خطأ في إنشاء الإعدادات الافتراضية: {e}")

    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """
        تنفيذ استعلام SELECT

        Args:
            query: الاستعلام
            params: المعاملات

        Returns:
            List[sqlite3.Row]: نتائج الاستعلام
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise

    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE

        Args:
            query: الاستعلام
            params: المعاملات

        Returns:
            int: عدد الصفوف المتأثرة
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"خطأ في تنفيذ التحديث: {e}")
            raise

    def get_setting(self, key: str) -> Optional[str]:
        """
        الحصول على قيمة إعداد

        Args:
            key: مفتاح الإعداد

        Returns:
            Optional[str]: قيمة الإعداد أو None
        """
        try:
            result = self.execute_query(
                'SELECT setting_value FROM system_settings WHERE setting_key = ?',
                (key,)
            )
            return result[0]['setting_value'] if result else None
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإعداد {key}: {e}")
            return None

    def set_setting(self, key: str, value: str, description: str = None) -> bool:
        """
        تعيين قيمة إعداد

        Args:
            key: مفتاح الإعداد
            value: قيمة الإعداد
            description: وصف الإعداد

        Returns:
            bool: True إذا تم التحديث بنجاح
        """
        try:
            if description:
                query = '''
                    INSERT OR REPLACE INTO system_settings
                    (setting_key, setting_value, description, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                '''
                params = (key, value, description)
            else:
                query = '''
                    INSERT OR REPLACE INTO system_settings
                    (setting_key, setting_value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                '''
                params = (key, value)

            self.execute_update(query, params)
            return True
        except Exception as e:
            logger.error(f"خطأ في تعيين الإعداد {key}: {e}")
            return False


    def log_action(self, user_id: Optional[int], action: str, details: str = None, ip_address: str = None):
        """
        تسجيل إجراء في سجلات النظام

        Args:
            user_id: معرف المستخدم
            action: الإجراء
            details: تفاصيل الإجراء
            ip_address: عنوان IP
        """
        try:
            self.execute_update('''
                INSERT INTO system_logs (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            ''', (user_id, action, details, ip_address))
        except Exception as e:
            logger.error(f"خطأ في تسجيل الإجراء: {e}")

    def cleanup_old_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            self.execute_update('''
                DELETE FROM user_sessions
                WHERE expires_at < CURRENT_TIMESTAMP OR is_active = FALSE
            ''')
            logger.info("تم تنظيف الجلسات المنتهية الصلاحية")
        except Exception as e:
            logger.error(f"خطأ في تنظيف الجلسات: {e}")

    def get_database_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات قاعدة البيانات

        Returns:
            Dict[str, Any]: إحصائيات قاعدة البيانات
        """
        try:
            stats = {}

            # عدد المستخدمين
            result = self.execute_query('SELECT COUNT(*) as count FROM users')
            stats['total_users'] = result[0]['count']

            # عدد المستخدمين النشطين
            result = self.execute_query('SELECT COUNT(*) as count FROM users WHERE is_active = TRUE')
            stats['active_users'] = result[0]['count']

            # عدد المديرين
            result = self.execute_query('SELECT COUNT(*) as count FROM users WHERE is_admin = TRUE')
            stats['admin_users'] = result[0]['count']

            # عدد الجلسات النشطة
            result = self.execute_query('''
                SELECT COUNT(*) as count FROM user_sessions
                WHERE is_active = TRUE AND expires_at > CURRENT_TIMESTAMP
            ''')
            stats['active_sessions'] = result[0]['count']

            # عدد سجلات النظام
            result = self.execute_query('SELECT COUNT(*) as count FROM system_logs')
            stats['total_logs'] = result[0]['count']

            # آخر تسجيل دخول
            result = self.execute_query('''
                SELECT MAX(last_login) as last_login FROM users
                WHERE last_login IS NOT NULL
            ''')
            stats['last_login'] = result[0]['last_login'] if result and result[0]['last_login'] else None

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {e}")
            return {}

    def _create_sample_data(self):
        """إنشاء بيانات وهمية للعرض"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # التحقق من وجود بيانات وهمية مسبقاً
                cursor.execute('SELECT COUNT(*) as count FROM reports')
                reports_count = cursor.fetchone()['count']

                if reports_count == 0:
                    # إضافة بلاغات وهمية
                    sample_reports = [
                        ('مشكلة في الخدمة', 'تأخير في الاستجابة للطلبات', 'خدمة', 'pending', 'high'),
                        ('شكوى من المستخدم', 'عدم رضا عن جودة الخدمة', 'شكوى', 'in_progress', 'medium'),
                        ('طلب تحسين', 'اقتراح لتحسين واجهة المستخدم', 'اقتراح', 'resolved', 'low'),
                        ('مشكلة تقنية', 'خطأ في النظام', 'تقني', 'pending', 'high'),
                        ('استفسار عام', 'سؤال حول كيفية الاستخدام', 'استفسار', 'resolved', 'low'),
                        ('بلاغ أمني', 'محاولة دخول غير مصرح', 'أمان', 'in_progress', 'high'),
                        ('طلب دعم', 'مساعدة في استخدام النظام', 'دعم', 'pending', 'medium'),
                        ('مشكلة في البيانات', 'خطأ في عرض المعلومات', 'بيانات', 'resolved', 'medium'),
                    ]

                    for title, desc, category, status, priority in sample_reports:
                        cursor.execute('''
                            INSERT INTO reports (title, description, category, status, priority)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (title, desc, category, status, priority))

                # التحقق من وجود غرامات وهمية
                cursor.execute('SELECT COUNT(*) as count FROM fines')
                fines_count = cursor.fetchone()['count']

                if fines_count == 0:
                    # إضافة غرامات وهمية
                    sample_fines = [
                        ('مخالفة سرعة', 500.00, 'تجاوز السرعة المحددة', 'paid'),
                        ('مخالفة وقوف', 150.00, 'وقوف في مكان ممنوع', 'pending'),
                        ('مخالفة إشارة', 300.00, 'تجاوز الإشارة الحمراء', 'pending'),
                        ('مخالفة حزام', 100.00, 'عدم ربط حزام الأمان', 'paid'),
                        ('مخالفة هاتف', 200.00, 'استخدام الهاتف أثناء القيادة', 'pending'),
                        ('مخالفة تأمين', 1000.00, 'القيادة بدون تأمين', 'overdue'),
                        ('مخالفة رخصة', 750.00, 'القيادة بدون رخصة سارية', 'pending'),
                        ('مخالفة بيئية', 400.00, 'انبعاثات ضارة', 'paid'),
                        ('مخالفة حمولة', 600.00, 'تجاوز الحمولة المسموحة', 'pending'),
                        ('مخالفة ضوضاء', 250.00, 'إزعاج بالضوضاء', 'paid'),
                    ]

                    from datetime import datetime, timedelta
                    for fine_type, amount, desc, status in sample_fines:
                        due_date = datetime.now() + timedelta(days=30)
                        cursor.execute('''
                            INSERT INTO fines (fine_type, amount, description, status, due_date)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (fine_type, amount, desc, status, due_date))

                conn.commit()
                logger.info("تم إنشاء البيانات الوهمية للعرض")

        except Exception as e:
            logger.error(f"خطأ في إنشاء البيانات الوهمية: {e}")

    def get_dashboard_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات لوحة التحكم

        Returns:
            Dict[str, Any]: إحصائيات لوحة التحكم
        """
        try:
            stats = {}

            # إحصائيات المستخدمين
            result = self.execute_query('SELECT COUNT(*) as count FROM users WHERE is_active = TRUE')
            stats['total_users'] = result[0]['count']

            # إحصائيات البلاغات
            result = self.execute_query('SELECT COUNT(*) as count FROM reports')
            stats['total_reports'] = result[0]['count']

            result = self.execute_query('SELECT COUNT(*) as count FROM reports WHERE status = "pending"')
            stats['pending_reports'] = result[0]['count']

            result = self.execute_query('SELECT COUNT(*) as count FROM reports WHERE status = "resolved"')
            stats['resolved_reports'] = result[0]['count']

            result = self.execute_query('SELECT COUNT(*) as count FROM reports WHERE status = "in_progress"')
            stats['in_progress_reports'] = result[0]['count']

            # إحصائيات الغرامات
            result = self.execute_query('SELECT COUNT(*) as count FROM fines')
            stats['total_fines_count'] = result[0]['count']

            result = self.execute_query('SELECT SUM(amount) as total FROM fines')
            stats['total_fines_amount'] = float(result[0]['total']) if result[0]['total'] else 0.0

            result = self.execute_query('SELECT SUM(amount) as total FROM fines WHERE status = "paid"')
            stats['paid_fines_amount'] = float(result[0]['total']) if result[0]['total'] else 0.0

            result = self.execute_query('SELECT SUM(amount) as total FROM fines WHERE status = "pending"')
            stats['pending_fines_amount'] = float(result[0]['total']) if result[0]['total'] else 0.0

            result = self.execute_query('SELECT COUNT(*) as count FROM fines WHERE status = "pending"')
            stats['pending_fines_count'] = result[0]['count']

            result = self.execute_query('SELECT COUNT(*) as count FROM fines WHERE status = "paid"')
            stats['paid_fines_count'] = result[0]['count']

            # إحصائيات إضافية
            result = self.execute_query('SELECT COUNT(*) as count FROM user_sessions WHERE is_active = TRUE AND expires_at > CURRENT_TIMESTAMP')
            stats['active_sessions'] = result[0]['count']

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات لوحة التحكم: {e}")
            return {}

# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()
