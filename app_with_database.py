#!/usr/bin/env python3
"""
تطبيق Mürşid مع قاعدة البيانات - <PERSON><PERSON>rşid App with Database
مثال على كيفية دمج قاعدة البيانات مع التطبيق الرئيسي
"""

import flet as ft
from app_database import app_db

def main(page: ft.Page):
    page.title = "Mürşid - مع قاعدة البيانات"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 400
    page.window_height = 600
    page.padding = 20

    # متغيرات حالة التطبيق
    if not hasattr(page, 'current_language'):
        page.current_language = "tr"
    current_language = page.current_language

    # النصوص متعددة اللغات
    texts = {
        "tr": {
            "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>",
            "email_label": "E-posta",
            "password_label": "<PERSON><PERSON><PERSON>",
            "login_button": "<PERSON><PERSON><PERSON>",
            "register_button": "<PERSON><PERSON>",
            "logout_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "welcome_message": "Hoş geldiniz",
            "admin_panel": "Yönetici Paneli",
            "user_count": "Kullanıcı Sayısı",
            "active_sessions": "Aktif Oturumlar",
        },
        "ar": {
            "title": "مرشد - تسجيل الدخول",
            "email_label": "البريد الإلكتروني",
            "password_label": "كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "logout_button": "تسجيل الخروج",
            "welcome_message": "مرحباً بك",
            "admin_panel": "لوحة الإدارة",
            "user_count": "عدد المستخدمين",
            "active_sessions": "الجلسات النشطة",
        }
    }

    def get_text(key):
        """الحصول على النص بناءً على اللغة الحالية"""
        return texts.get(current_language, texts["tr"]).get(key, key)

    def show_message(message, is_error=False):
        """عرض رسالة للمستخدم"""
        page.snack_bar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.Colors.RED_400 if is_error else ft.Colors.GREEN_400
        )
        page.snack_bar.open = True
        page.update()

    def login_clicked(e):
        """معالج تسجيل الدخول"""
        email = email_field.value
        password = password_field.value

        if not email or not password:
            show_message("يرجى ملء جميع الحقول", True)
            return

        # محاولة تسجيل الدخول باستخدام قاعدة البيانات
        success, message, user_data = app_db.authenticate_user(email, password)

        if success:
            show_message(f"{get_text('welcome_message')} {user_data['first_name']}!")
            show_main_page()
        else:
            show_message(message, True)

    def logout_clicked(e):
        """معالج تسجيل الخروج"""
        app_db.logout_user()
        show_login_page()
        show_message("تم تسجيل الخروج بنجاح")

    def register_clicked(e):
        """معالج التسجيل"""
        show_register_page()

    def show_login_page():
        """عرض صفحة تسجيل الدخول"""
        page.clean()
        
        page.add(
            ft.Column([
                ft.Text("🛡️", size=60, text_align=ft.TextAlign.CENTER),
                ft.Text("Mürşid", size=24, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                ft.Text(get_text("title"), size=16, text_align=ft.TextAlign.CENTER),
                
                ft.Container(height=30),
                
                email_field,
                ft.Container(height=10),
                password_field,
                
                ft.Container(height=20),
                
                login_button,
                ft.Container(height=10),
                register_button,
                
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        )

    def show_main_page():
        """عرض الصفحة الرئيسية بعد تسجيل الدخول"""
        user = app_db.get_current_user()
        if not user:
            show_login_page()
            return

        page.clean()

        # إنشاء المحتوى الأساسي
        content = [
            ft.Text(f"{get_text('welcome_message')} {user['first_name']} {user['last_name']}!", 
                   size=20, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
            
            ft.Container(height=20),
            
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("معلومات المستخدم", size=16, weight=ft.FontWeight.BOLD),
                        ft.Text(f"الاسم: {user['first_name']} {user['last_name']}"),
                        ft.Text(f"البريد الإلكتروني: {user['email']}"),
                        ft.Text(f"نوع الحساب: {'مدير' if user['is_admin'] else 'مستخدم عادي'}"),
                    ]),
                    padding=20
                )
            ),
            
            ft.Container(height=20),
            
            logout_button,
        ]

        # إضافة لوحة الإدارة للمديرين
        if app_db.is_admin():
            stats = app_db.get_system_stats()
            
            admin_panel = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(get_text("admin_panel"), size=16, weight=ft.FontWeight.BOLD),
                        ft.Text(f"{get_text('user_count')}: {stats.get('total_users', 0)}"),
                        ft.Text(f"{get_text('active_sessions')}: {stats.get('active_sessions', 0)}"),
                        ft.Text(f"المستخدمين النشطين: {stats.get('active_users', 0)}"),
                        ft.Text(f"إجمالي السجلات: {stats.get('total_logs', 0)}"),
                    ]),
                    padding=20
                ),
                color=ft.Colors.BLUE_50
            )
            
            content.insert(-2, admin_panel)
            content.insert(-2, ft.Container(height=10))

        page.add(
            ft.Column(content, horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        )

    def show_register_page():
        """عرض صفحة التسجيل"""
        page.clean()

        # حقول التسجيل
        first_name_field = ft.TextField(label="الاسم الأول", expand=True)
        last_name_field = ft.TextField(label="اسم العائلة", expand=True)
        kimlik_field = ft.TextField(label="رقم الكيمليك", expand=True, keyboard_type=ft.KeyboardType.NUMBER)
        phone_field = ft.TextField(label="رقم الهاتف", expand=True)
        birth_date_field = ft.TextField(label="تاريخ الميلاد (YYYY-MM-DD)", expand=True)
        address_field = ft.TextField(label="العنوان", expand=True, multiline=True)
        reg_email_field = ft.TextField(label="البريد الإلكتروني", expand=True)
        reg_password_field = ft.TextField(label="كلمة المرور", password=True, expand=True)
        confirm_password_field = ft.TextField(label="تأكيد كلمة المرور", password=True, expand=True)

        def register_submit(e):
            """معالج إرسال التسجيل"""
            # جمع البيانات
            registration_data = {
                'first_name': first_name_field.value,
                'last_name': last_name_field.value,
                'kimlik_number': kimlik_field.value,
                'phone_number': phone_field.value,
                'birth_date': birth_date_field.value,
                'address': address_field.value,
                'email': reg_email_field.value,
                'password': reg_password_field.value,
                'confirm_password': confirm_password_field.value,
            }

            # التحقق من البيانات
            is_valid, message = app_db.validate_registration_data(registration_data)
            if not is_valid:
                show_message(message, True)
                return

            # محاولة التسجيل
            success, message = app_db.register_user(registration_data)
            if success:
                show_message("تم إنشاء الحساب بنجاح!")
                show_login_page()
            else:
                show_message(message, True)

        def back_to_login(e):
            """العودة لصفحة تسجيل الدخول"""
            show_login_page()

        page.add(
            ft.Column([
                ft.Text("إنشاء حساب جديد", size=20, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                
                ft.Container(height=20),
                
                first_name_field,
                last_name_field,
                kimlik_field,
                phone_field,
                birth_date_field,
                address_field,
                reg_email_field,
                reg_password_field,
                confirm_password_field,
                
                ft.Container(height=20),
                
                ft.ElevatedButton("إنشاء الحساب", on_click=register_submit, width=200),
                ft.TextButton("العودة لتسجيل الدخول", on_click=back_to_login),
                
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, scroll=ft.ScrollMode.AUTO)
        )

    # إنشاء عناصر واجهة المستخدم
    email_field = ft.TextField(label=get_text("email_label"), expand=True)
    password_field = ft.TextField(label=get_text("password_label"), password=True, expand=True)
    login_button = ft.ElevatedButton(get_text("login_button"), on_click=login_clicked, width=200)
    register_button = ft.TextButton(get_text("register_button"), on_click=register_clicked)
    logout_button = ft.ElevatedButton(get_text("logout_button"), on_click=logout_clicked, width=200)

    # التحقق من حالة تسجيل الدخول
    if app_db.is_user_logged_in():
        show_main_page()
    else:
        show_login_page()

if __name__ == "__main__":
    ft.app(target=main)
