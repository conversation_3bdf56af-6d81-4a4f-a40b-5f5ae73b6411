#!/usr/bin/env python3
"""
نماذج البيانات - Mürşid Data Models
تعريف نماذج البيانات والهياكل
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

@dataclass
class User:
    """نموذج المستخدم"""
    id: Optional[int] = None
    first_name: str = ""
    last_name: str = ""
    kimlik_number: str = ""
    phone_number: str = ""
    birth_date: str = ""
    address: str = ""
    email: str = ""
    password_hash: str = ""
    is_admin: bool = False
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    @property
    def full_name(self) -> str:
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}".strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'kimlik_number': self.kimlik_number,
            'phone_number': self.phone_number,
            'birth_date': self.birth_date,
            'address': self.address,
            'email': self.email,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """إنشاء من قاموس"""
        return cls(
            id=data.get('id'),
            first_name=data.get('first_name', ''),
            last_name=data.get('last_name', ''),
            kimlik_number=data.get('kimlik_number', ''),
            phone_number=data.get('phone_number', ''),
            birth_date=data.get('birth_date', ''),
            address=data.get('address', ''),
            email=data.get('email', ''),
            password_hash=data.get('password_hash', ''),
            is_admin=data.get('is_admin', False),
            is_active=data.get('is_active', True),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            last_login=data.get('last_login'),
        )

@dataclass
class UserSession:
    """نموذج جلسة المستخدم"""
    id: Optional[int] = None
    user_id: int = 0
    session_token: str = ""
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    is_active: bool = True
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_token': self.session_token,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserSession':
        """إنشاء من قاموس"""
        return cls(
            id=data.get('id'),
            user_id=data.get('user_id', 0),
            session_token=data.get('session_token', ''),
            created_at=data.get('created_at'),
            expires_at=data.get('expires_at'),
            is_active=data.get('is_active', True),
            ip_address=data.get('ip_address'),
            user_agent=data.get('user_agent'),
        )

@dataclass
class SystemLog:
    """نموذج سجل النظام"""
    id: Optional[int] = None
    user_id: Optional[int] = None
    action: str = ""
    details: Optional[str] = None
    ip_address: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'details': self.details,
            'ip_address': self.ip_address,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemLog':
        """إنشاء من قاموس"""
        return cls(
            id=data.get('id'),
            user_id=data.get('user_id'),
            action=data.get('action', ''),
            details=data.get('details'),
            ip_address=data.get('ip_address'),
            timestamp=data.get('timestamp'),
        )

@dataclass
class SystemSetting:
    """نموذج إعدادات النظام"""
    id: Optional[int] = None
    setting_key: str = ""
    setting_value: str = ""
    description: Optional[str] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'setting_key': self.setting_key,
            'setting_value': self.setting_value,
            'description': self.description,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SystemSetting':
        """إنشاء من قاموس"""
        return cls(
            id=data.get('id'),
            setting_key=data.get('setting_key', ''),
            setting_value=data.get('setting_value', ''),
            description=data.get('description'),
            updated_at=data.get('updated_at'),
        )

@dataclass
class FailedLoginAttempt:
    """نموذج محاولة تسجيل الدخول الفاشلة"""
    id: Optional[int] = None
    email: str = ""
    ip_address: Optional[str] = None
    attempt_time: Optional[datetime] = None
    reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'email': self.email,
            'ip_address': self.ip_address,
            'attempt_time': self.attempt_time.isoformat() if self.attempt_time else None,
            'reason': self.reason,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FailedLoginAttempt':
        """إنشاء من قاموس"""
        return cls(
            id=data.get('id'),
            email=data.get('email', ''),
            ip_address=data.get('ip_address'),
            attempt_time=data.get('attempt_time'),
            reason=data.get('reason'),
        )

@dataclass
class RegistrationData:
    """نموذج بيانات التسجيل"""
    first_name: str = ""
    last_name: str = ""
    kimlik_number: str = ""
    phone_number: str = ""
    birth_date: str = ""
    address: str = ""
    email: str = ""
    password: str = ""
    confirm_password: str = ""
    
    def validate(self) -> tuple[bool, str]:
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        required_fields = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'kimlik_number': 'رقم الكيمليك',
            'phone_number': 'رقم الهاتف',
            'birth_date': 'تاريخ الميلاد',
            'address': 'العنوان',
            'email': 'البريد الإلكتروني',
            'password': 'كلمة المرور',
            'confirm_password': 'تأكيد كلمة المرور'
        }
        
        for field, label in required_fields.items():
            value = getattr(self, field, '').strip()
            if not value:
                return False, f"{label} مطلوب"
        
        # التحقق من تطابق كلمات المرور
        if self.password != self.confirm_password:
            return False, "كلمات المرور غير متطابقة"
        
        # التحقق من طول الاسم
        if len(self.first_name.strip()) < 2:
            return False, "الاسم الأول يجب أن يكون على الأقل حرفين"
        
        if len(self.last_name.strip()) < 2:
            return False, "اسم العائلة يجب أن يكون على الأقل حرفين"
        
        return True, "البيانات صحيحة"
    
    def to_user_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس المستخدم"""
        return {
            'first_name': self.first_name.strip(),
            'last_name': self.last_name.strip(),
            'kimlik_number': self.kimlik_number.strip(),
            'phone_number': self.phone_number.strip(),
            'birth_date': self.birth_date,
            'address': self.address.strip(),
            'email': self.email.strip().lower(),
            'password': self.password
        }

@dataclass
class LoginData:
    """نموذج بيانات تسجيل الدخول"""
    email: str = ""
    password: str = ""
    remember_me: bool = False
    
    def validate(self) -> tuple[bool, str]:
        """التحقق من صحة البيانات"""
        if not self.email.strip():
            return False, "البريد الإلكتروني مطلوب"
        
        if not self.password:
            return False, "كلمة المرور مطلوبة"
        
        return True, "البيانات صحيحة"

# ثوابت النظام
class SystemConstants:
    """ثوابت النظام"""
    
    # أنواع الإجراءات
    ACTION_USER_LOGIN = "USER_LOGIN"
    ACTION_USER_LOGOUT = "USER_LOGOUT"
    ACTION_USER_REGISTERED = "USER_REGISTERED"
    ACTION_PASSWORD_CHANGED = "PASSWORD_CHANGED"
    ACTION_PROFILE_UPDATED = "PROFILE_UPDATED"
    ACTION_USER_DEACTIVATED = "USER_DEACTIVATED"
    ACTION_USER_ACTIVATED = "USER_ACTIVATED"
    ACTION_ADMIN_LOGIN = "ADMIN_LOGIN"
    ACTION_SETTINGS_UPDATED = "SETTINGS_UPDATED"
    
    # حالات المستخدم
    USER_STATUS_ACTIVE = True
    USER_STATUS_INACTIVE = False
    
    # أنواع المستخدمين
    USER_TYPE_ADMIN = True
    USER_TYPE_REGULAR = False
    
    # إعدادات الجلسة
    SESSION_TIMEOUT_HOURS = 1
    SESSION_CLEANUP_INTERVAL = 3600  # ثانية
    
    # إعدادات الأمان
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    PASSWORD_MIN_LENGTH = 8
    
    # أحجام الحقول
    MAX_NAME_LENGTH = 100
    MAX_EMAIL_LENGTH = 254
    MAX_PHONE_LENGTH = 20
    MAX_ADDRESS_LENGTH = 500
    KIMLIK_LENGTH = 11
