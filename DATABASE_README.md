# قاعدة البيانات - Mürşid Database

## نظرة عامة

تم إنشاء قاعدة بيانات شاملة لتطبيق Mürşid تتضمن جميع الوظائف المطلوبة لإدارة المستخدمين والأمان.

## الملفات المنشأة

### 1. `database.py`
- **الوصف**: مدير قاعدة البيانات الرئيسي
- **الوظائف**:
  - إنشاء وإدارة قاعدة البيانات SQLite
  - إنشاء الجداول والفهارس
  - إدارة الإعدادات والسجلات
  - إحصائيات النظام

### 2. `security.py`
- **الوصف**: مدير الأمان والتشفير
- **الوظائف**:
  - تشفير كلمات المرور باستخدام SHA-256 + Salt
  - التحقق من صحة البيانات (البريد الإلكتروني، رقم الكيمليك، رقم الهاتف)
  - إنشاء كلمات مرور آمنة
  - إدارة رموز الجلسات

### 3. `user_manager.py`
- **الوصف**: مدير المستخدمين
- **الوظائف**:
  - تسجيل المستخدمين الجدد
  - تسجيل الدخول والخروج
  - إدارة الجلسات
  - تحديث الملفات الشخصية
  - تغيير كلمات المرور

### 4. `models.py`
- **الوصف**: نماذج البيانات
- **المحتوى**:
  - فئات البيانات (User, UserSession, SystemLog, إلخ)
  - نماذج التحقق من البيانات
  - ثوابت النظام

### 5. `app_database.py`
- **الوصف**: تكامل قاعدة البيانات مع التطبيق
- **الوظائف**:
  - واجهة مبسطة للتطبيق الرئيسي
  - إدارة حالة المستخدم الحالي
  - التحقق من الصلاحيات

### 6. `test_database.py`
- **الوصف**: اختبارات قاعدة البيانات
- **الوظائف**:
  - اختبار جميع وظائف قاعدة البيانات
  - التحقق من صحة العمليات
  - تنظيف البيانات التجريبية

## هيكل قاعدة البيانات

### جدول المستخدمين (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    kimlik_number TEXT UNIQUE NOT NULL,
    phone_number TEXT NOT NULL,
    birth_date TEXT NOT NULL,
    address TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);
```

### جدول الجلسات (user_sessions)
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    ip_address TEXT,
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### جدول السجلات (system_logs)
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    details TEXT,
    ip_address TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### جدول الإعدادات (system_settings)
```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول المحاولات الفاشلة (failed_login_attempts)
```sql
CREATE TABLE failed_login_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT NOT NULL,
    ip_address TEXT,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reason TEXT
);
```

## الميزات الأمنية

### 1. تشفير كلمات المرور
- استخدام SHA-256 مع Salt عشوائي
- كل كلمة مرور لها مفتاح تشفير فريد
- مقارنة آمنة للكلمات المشفرة

### 2. إدارة الجلسات
- رموز جلسات آمنة وعشوائية
- انتهاء صلاحية تلقائي (ساعة واحدة)
- تنظيف الجلسات المنتهية الصلاحية

### 3. حماية من الهجمات
- قفل الحساب بعد 5 محاولات دخول فاشلة
- تسجيل جميع المحاولات الفاشلة
- التحقق من أمان النصوص المدخلة

### 4. التحقق من البيانات
- التحقق من صحة البريد الإلكتروني
- التحقق من رقم الكيمليك التركي
- التحقق من رقم الهاتف
- التحقق من قوة كلمة المرور

## المستخدم الإداري الافتراضي

```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
```

## الإعدادات الافتراضية

- `app_name`: Mürşid
- `app_version`: 1.0.0
- `max_login_attempts`: 5
- `session_timeout`: 3600 (ثانية)
- `password_min_length`: 8
- `default_language`: tr

## كيفية الاستخدام

### 1. تشغيل الاختبارات
```bash
python test_database.py
```

### 2. استخدام قاعدة البيانات في التطبيق
```python
from app_database import app_db

# تسجيل دخول
success, message, user_data = app_db.authenticate_user("<EMAIL>", "password")

# تسجيل مستخدم جديد
user_data = {
    'first_name': 'أحمد',
    'last_name': 'محمد',
    'kimlik_number': '12345678901',
    'phone_number': '+90 ************',
    'birth_date': '1990-01-01',
    'address': 'إسطنبول، تركيا',
    'email': '<EMAIL>',
    'password': 'StrongPassword123!',
    'confirm_password': 'StrongPassword123!'
}
success, message = app_db.register_user(user_data)
```

### 3. التحقق من حالة المستخدم
```python
# التحقق من تسجيل الدخول
if app_db.is_user_logged_in():
    user = app_db.get_current_user()
    print(f"مرحباً {user['first_name']}")

# التحقق من صلاحيات المدير
if app_db.is_admin():
    stats = app_db.get_system_stats()
    users = app_db.get_all_users()
```

## الصيانة

### تنظيف الجلسات المنتهية الصلاحية
```python
from database import db_manager
db_manager.cleanup_old_sessions()
```

### الحصول على إحصائيات النظام
```python
stats = db_manager.get_database_stats()
print(f"إجمالي المستخدمين: {stats['total_users']}")
print(f"الجلسات النشطة: {stats['active_sessions']}")
```

## ملاحظات مهمة

1. **ملف قاعدة البيانات**: يتم إنشاء ملف `mursid.db` تلقائياً
2. **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية دورية من ملف قاعدة البيانات
3. **الأمان**: جميع كلمات المرور مشفرة ولا يمكن استرجاعها
4. **السجلات**: يتم تسجيل جميع العمليات المهمة في جدول السجلات

## التطوير المستقبلي

- إضافة تشفير قاعدة البيانات
- تحسين خوارزمية التحقق من رقم الكيمليك
- إضافة نظام إشعارات
- تحسين نظام إدارة الصلاحيات
