#!/usr/bin/env python3
"""
Mürşid Giriş Uygulaması - Mürşid Login Application
"""

import flet as ft
from app_database import app_db

def main(page: ft.Page):
    page.title = "<PERSON><PERSON><PERSON> - Mürşid"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.window_width = 350  # Varsayılan boyut
    page.window_height = 500  # Varsayılan boyut
    page.window_resizable = True  # Boyut değiştirilebilir
    page.window_min_width = 280  # Minimum genişlik
    page.window_min_height = 400  # Minimum yükseklik
    page.padding = 10  # Sayfa dolgusu

    # Kullanıcı verilerini kaydetmek için değişkenler
    if not hasattr(page, 'current_language'):
        page.current_language = "tr"  # Varsayılan dil: Türkçe
    current_language = page.current_language

    print(f"Current language: {current_language}")  # للتحقق من اللغة الحالية

    # Dil metinleri
    texts = {
        "tr": {
            "title": "<PERSON><PERSON><PERSON> - <PERSON>ü<PERSON>ş<PERSON>",
            "login_title": "<PERSON><PERSON><PERSON> Yap",
            "welcome": "<PERSON>ürşid uygulamasına hoş geldiniz",
            "email_label": "E-posta",
            "email_hint": "<EMAIL>",
            "password_label": "Şifre",
            "password_hint": "Şifrenizi girin",
            "login_button": "Giriş Yap",
            "register_button": "Yeni hesap oluştur",
            "forgot_button": "Şifremi unuttum",
            "email_error": "Lütfen e-posta adresinizi girin",
            "password_error": "Lütfen şifrenizi girin",
            "success_message": "Hoş geldiniz! Giriş başarılı\nE-posta: ",
            "admin_welcome": "Yönetici Paneline Hoş Geldiniz!",
            "admin_dashboard": "Yönetici Paneli",
            "admin_users": "Kullanıcı Yönetimi",
            "admin_settings": "Sistem Ayarları",
            "admin_reports": "Raporlar",
            "admin_logs": "Sistem Günlükleri",
            "admin_backup": "Yedekleme",
            "admin_security": "Güvenlik",
            "admin_logout": "Çıkış Yap",
            "admin_user_count": "Toplam Kullanıcı",
            "admin_active_sessions": "Aktif Oturumlar",
            "admin_system_status": "Sistem Durumu",
            "admin_last_backup": "Son Yedekleme",
            "about": "Hakkında",
            "help": "Yardım",
            "settings": "Ayarlar",
            "language": "Dil",
            "theme": "Tema",
            "privacy": "Gizlilik",
            "about_text": "Mürşid v1.0 - Güvenli Giriş Uygulaması",
            "help_text": "Yardım: E-posta ve şifrenizi girin",
            "settings_text": "Ayarlar sayfası yakında eklenecek",
            "theme_text": "Tema değiştirme yakında eklenecek",
            "privacy_text": "Gizlilik Politikası yakında eklenecek",
            "register_text": "Kayıt sayfası yakında eklenecek",
            "forgot_text": "Şifre sıfırlama yakında eklenecek",
            "language_name": "Türkçe"
        },
        "ar": {
            "title": "تسجيل الدخول - Mürşid",
            "login_title": "تسجيل الدخول",
            "welcome": "مرحباً بك في تطبيق Mürşid",
            "email_label": "البريد الإلكتروني",
            "email_hint": "<EMAIL>",
            "password_label": "كلمة المرور",
            "password_hint": "أدخل كلمة المرور",
            "login_button": "تسجيل الدخول",
            "register_button": "إنشاء حساب جديد",
            "forgot_button": "نسيت كلمة المرور؟",
            "email_error": "يرجى إدخال البريد الإلكتروني",
            "password_error": "يرجى إدخال كلمة المرور",
            "success_message": "مرحباً! تم تسجيل الدخول بنجاح\nالبريد الإلكتروني: ",
            "admin_welcome": "مرحباً بك في لوحة الإدارة!",
            "admin_dashboard": "لوحة الإدارة",
            "admin_users": "إدارة المستخدمين",
            "admin_settings": "إعدادات النظام",
            "admin_reports": "التقارير",
            "admin_logs": "سجلات النظام",
            "admin_backup": "النسخ الاحتياطي",
            "admin_security": "الأمان",
            "admin_logout": "تسجيل الخروج",
            "admin_user_count": "إجمالي المستخدمين",
            "admin_active_sessions": "الجلسات النشطة",
            "admin_system_status": "حالة النظام",
            "admin_last_backup": "آخر نسخة احتياطية",
            "about": "حول التطبيق",
            "help": "المساعدة",
            "settings": "الإعدادات",
            "language": "اللغة",
            "theme": "المظهر",
            "privacy": "الخصوصية",
            "about_text": "Mürşid الإصدار 1.0 - تطبيق دخول آمن",
            "help_text": "المساعدة: أدخل بريدك الإلكتروني وكلمة المرور",
            "settings_text": "صفحة الإعدادات ستضاف قريباً",
            "theme_text": "تغيير المظهر سيضاف قريباً",
            "privacy_text": "سياسة الخصوصية ستضاف قريباً",
            "register_text": "صفحة التسجيل ستضاف قريباً",
            "forgot_text": "استعادة كلمة المرور ستضاف قريباً",
            "language_name": "العربية"
        },
        "en": {
            "title": "Login - Mürşid",
            "login_title": "Login",
            "welcome": "Welcome to Mürşid application",
            "email_label": "Email",
            "email_hint": "<EMAIL>",
            "password_label": "Password",
            "password_hint": "Enter your password",
            "login_button": "Login",
            "register_button": "Create new account",
            "forgot_button": "Forgot password?",
            "email_error": "Please enter your email",
            "password_error": "Please enter your password",
            "success_message": "Welcome! Login successful\nEmail: ",
            "admin_welcome": "Welcome to Admin Dashboard!",
            "admin_dashboard": "Admin Dashboard",
            "admin_users": "User Management",
            "admin_settings": "System Settings",
            "admin_reports": "Reports",
            "admin_logs": "System Logs",
            "admin_backup": "Backup",
            "admin_security": "Security",
            "admin_logout": "Logout",
            "admin_user_count": "Total Users",
            "admin_active_sessions": "Active Sessions",
            "admin_system_status": "System Status",
            "admin_last_backup": "Last Backup",
            "about": "About",
            "help": "Help",
            "settings": "Settings",
            "language": "Language",
            "theme": "Theme",
            "privacy": "Privacy",
            "about_text": "Mürşid v1.0 - Secure Login Application",
            "help_text": "Help: Enter your email and password",
            "settings_text": "Settings page coming soon",
            "theme_text": "Theme changing coming soon",
            "privacy_text": "Privacy Policy coming soon",
            "register_text": "Registration page coming soon",
            "forgot_text": "Password reset coming soon",
            "language_name": "English"
        }
    }

    # Sayfa başlığını ayarla
    page.title = texts[current_language]["title"]



    # Ayarlar menüsü fonksiyonları
    def show_about(e):
        print("About clicked!")  # للتحقق

        # استخدام صفحة جديدة لحول التطبيق
        def close_about():
            page.clean()
            main(page)



        # مسح الصفحة الحالية وإظهار صفحة حول التطبيق
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_about()
                        ),
                        ft.Text(
                            "Hakkında" if current_language == "tr"
                            else "حول التطبيق" if current_language == "ar"
                            else "About",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # شعار التطبيق
                    ft.Text("🛡️", size=80, text_align=ft.TextAlign.CENTER),

                    ft.Text(
                        "Mürşid",
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_700
                    ),

                    ft.Text(
                        "Güvenli Giriş Uygulaması" if current_language == "tr"
                        else "تطبيق دخول آمن" if current_language == "ar"
                        else "Secure Login Application",
                        size=16,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.GREY_600
                    ),

                    ft.Container(height=20),

                    # معلومات الإصدار
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📱 Sürüm Bilgileri" if current_language == "tr"
                                else "📱 معلومات الإصدار" if current_language == "ar"
                                else "📱 Version Info",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.GREEN_700
                            ),
                            ft.Text(
                                "Sürüm: 1.0.0\nYayın Tarihi: Ocak 2024\nPlatform: Windows, macOS, Linux" if current_language == "tr"
                                else "الإصدار: 1.0.0\nتاريخ الإصدار: يناير 2024\nالمنصة: Windows, macOS, Linux" if current_language == "ar"
                                else "Version: 1.0.0\nRelease Date: January 2024\nPlatform: Windows, macOS, Linux",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=15),

                    # وصف التطبيق
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📝 Açıklama" if current_language == "tr"
                                else "📝 الوصف" if current_language == "ar"
                                else "📝 Description",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_700
                            ),
                            ft.Text(
                                "Mürşid, kullanıcıların güvenli bir şekilde giriş yapabilmelerini sağlayan modern bir uygulamadır. Gelişmiş güvenlik özellikleri, çok dilli destek ve kullanıcı dostu arayüzü ile öne çıkar." if current_language == "tr"
                                else "مرشد هو تطبيق حديث يمكّن المستخدمين من تسجيل الدخول بأمان. يتميز بميزات أمان متقدمة ودعم متعدد اللغات وواجهة سهلة الاستخدام." if current_language == "ar"
                                else "Mürşid is a modern application that enables users to log in securely. It stands out with advanced security features, multi-language support, and user-friendly interface.",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=15),

                    # الميزات
                    ft.Text(
                        "✨ Özellikler" if current_language == "tr"
                        else "✨ الميزات" if current_language == "ar"
                        else "✨ Features",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Gelişmiş Güvenlik" if current_language == "tr"
                            else "أمان متقدم" if current_language == "ar"
                            else "Advanced Security"
                        ),
                        subtitle=ft.Text(
                            "İki faktörlü kimlik doğrulama ve şifreleme" if current_language == "tr"
                            else "مصادقة ثنائية وتشفير" if current_language == "ar"
                            else "Two-factor authentication and encryption"
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LANGUAGE, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Çok Dilli Destek" if current_language == "tr"
                            else "دعم متعدد اللغات" if current_language == "ar"
                            else "Multi-language Support"
                        ),
                        subtitle=ft.Text(
                            "Türkçe, Arapça ve İngilizce" if current_language == "tr"
                            else "التركية والعربية والإنجليزية" if current_language == "ar"
                            else "Turkish, Arabic, and English"
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PRIVACY_TIP, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Gizlilik Koruması" if current_language == "tr"
                            else "حماية الخصوصية" if current_language == "ar"
                            else "Privacy Protection"
                        ),
                        subtitle=ft.Text(
                            "Verileriniz güvende ve korunuyor" if current_language == "tr"
                            else "بياناتك آمنة ومحمية" if current_language == "ar"
                            else "Your data is safe and protected"
                        )
                    ),

                    ft.Container(height=15),

                    # معلومات التواصل
                    ft.Text(
                        "📞 İletişim" if current_language == "tr"
                        else "📞 التواصل" if current_language == "ar"
                        else "📞 Contact",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.TEAL_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.EMAIL, color=ft.Colors.TEAL),
                        title=ft.Text("<EMAIL>"),
                        subtitle=ft.Text(
                            "Destek ve yardım için" if current_language == "tr"
                            else "للدعم والمساعدة" if current_language == "ar"
                            else "For support and help"
                        )
                    ),

                    ft.Container(height=20),

                    # حقوق النشر
                    ft.Container(
                        content=ft.Text(
                            "© 2024 Mürşid Team. Tüm hakları saklıdır." if current_language == "tr"
                            else "© 2024 فريق مرشد. جميع الحقوق محفوظة." if current_language == "ar"
                            else "© 2024 Mürşid Team. All rights reserved.",
                            size=12,
                            color=ft.Colors.GREY_500,
                            text_align=ft.TextAlign.CENTER
                        ),
                        padding=10
                    ),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_about()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    # Dil seçimi fonksiyonları
    def change_to_turkish(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "tr"
        page.snack_bar = ft.SnackBar(content=ft.Text("Dil Türkçe olarak değiştirildi"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_arabic(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "ar"
        page.snack_bar = ft.SnackBar(content=ft.Text("تم تغيير اللغة إلى العربية"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def change_to_english(e):
        if hasattr(page, 'dialog') and page.dialog:
            page.dialog.open = False
        page.current_language = "en"
        page.snack_bar = ft.SnackBar(content=ft.Text("Language changed to English"))
        page.snack_bar.open = True
        page.update()
        # Sayfayı yeniden yükle
        import time
        time.sleep(0.5)
        page.clean()
        main(page)

    def show_help(e):
        print("Help clicked!")  # للتحقق

        # استخدام صفحة جديدة للمساعدة
        def close_help():
            page.clean()
            main(page)

        def show_faq_item(question, answer):
            page.snack_bar = ft.SnackBar(content=ft.Text(answer))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة المساعدة
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_help()
                        ),
                        ft.Text(
                            "Yardım" if current_language == "tr"
                            else "المساعدة" if current_language == "ar"
                            else "Help",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة المساعدة
                    ft.Icon(ft.Icons.HELP_CENTER, size=60, color=ft.Colors.BLUE_600),

                    ft.Text(
                        "Size nasıl yardımcı olabiliriz?" if current_language == "tr"
                        else "كيف يمكننا مساعدتك؟" if current_language == "ar"
                        else "How can we help you?",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_700
                    ),

                    ft.Container(height=20),

                    # دليل البدء السريع
                    ft.Text(
                        "🚀 Hızlı Başlangıç" if current_language == "tr"
                        else "🚀 البدء السريع" if current_language == "ar"
                        else "🚀 Quick Start",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREEN_600
                    ),

                    ft.Container(
                        content=ft.Column([
                            ft.ListTile(
                                leading=ft.Text("1️⃣", size=20),
                                title=ft.Text(
                                    "E-posta adresinizi girin" if current_language == "tr"
                                    else "أدخل عنوان بريدك الإلكتروني" if current_language == "ar"
                                    else "Enter your email address"
                                ),
                                subtitle=ft.Text(
                                    "Geçerli bir e-posta adresi kullanın" if current_language == "tr"
                                    else "استخدم عنوان بريد إلكتروني صالح" if current_language == "ar"
                                    else "Use a valid email address"
                                )
                            ),
                            ft.ListTile(
                                leading=ft.Text("2️⃣", size=20),
                                title=ft.Text(
                                    "Güçlü bir şifre oluşturun" if current_language == "tr"
                                    else "أنشئ كلمة مرور قوية" if current_language == "ar"
                                    else "Create a strong password"
                                ),
                                subtitle=ft.Text(
                                    "En az 8 karakter, büyük/küçük harf ve rakam" if current_language == "tr"
                                    else "8 أحرف على الأقل، أحرف كبيرة/صغيرة وأرقام" if current_language == "ar"
                                    else "At least 8 characters, upper/lower case and numbers"
                                )
                            ),
                            ft.ListTile(
                                leading=ft.Text("3️⃣", size=20),
                                title=ft.Text(
                                    "Giriş butonuna tıklayın" if current_language == "tr"
                                    else "اضغط على زر تسجيل الدخول" if current_language == "ar"
                                    else "Click the login button"
                                ),
                                subtitle=ft.Text(
                                    "Bilgileriniz doğrulanacak" if current_language == "tr"
                                    else "سيتم التحقق من معلوماتك" if current_language == "ar"
                                    else "Your information will be verified"
                                )
                            )
                        ]),
                        padding=10,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=20),

                    # الأسئلة الشائعة
                    ft.Text(
                        "❓ Sık Sorulan Sorular" if current_language == "tr"
                        else "❓ الأسئلة الشائعة" if current_language == "ar"
                        else "❓ Frequently Asked Questions",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PURPLE_600
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOCK, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Şifremi unuttum, ne yapmalıyım?" if current_language == "tr"
                            else "نسيت كلمة المرور، ماذا أفعل؟" if current_language == "ar"
                            else "I forgot my password, what should I do?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Şifre sıfırlama",
                            "E-posta adresinizi girin, size sıfırlama bağlantısı göndereceğiz." if current_language == "tr"
                            else "أدخل عنوان بريدك الإلكتروني، سنرسل لك رابط إعادة التعيين." if current_language == "ar"
                            else "Enter your email address, we'll send you a reset link."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.RED),
                        title=ft.Text(
                            "Hesabım güvenli mi?" if current_language == "tr"
                            else "هل حسابي آمن؟" if current_language == "ar"
                            else "Is my account secure?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Güvenlik",
                            "Evet! Gelişmiş şifreleme ve iki faktörlü kimlik doğrulama kullanıyoruz." if current_language == "tr"
                            else "نعم! نستخدم تشفيراً متقدماً ومصادقة ثنائية." if current_language == "ar"
                            else "Yes! We use advanced encryption and two-factor authentication."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LANGUAGE, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Dili nasıl değiştirebilirim?" if current_language == "tr"
                            else "كيف يمكنني تغيير اللغة؟" if current_language == "ar"
                            else "How can I change the language?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Dil değiştirme",
                            "Üst menüden ⋮ → Dil seçeneğini kullanın." if current_language == "tr"
                            else "استخدم خيار ⋮ ← اللغة من القائمة العلوية." if current_language == "ar"
                            else "Use ⋮ → Language option from the top menu."
                        )
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.PRIVACY_TIP, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Verilerim nasıl korunuyor?" if current_language == "tr"
                            else "كيف يتم حماية بياناتي؟" if current_language == "ar"
                            else "How is my data protected?"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_faq_item(
                            "Veri koruması",
                            "Verileriniz şifrelenir ve güvenli sunucularda saklanır." if current_language == "tr"
                            else "يتم تشفير بياناتك وحفظها في خوادم آمنة." if current_language == "ar"
                            else "Your data is encrypted and stored on secure servers."
                        )
                    ),

                    ft.Container(height=20),

                    # معلومات الاتصال للدعم
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "📞 Destek İletişim" if current_language == "tr"
                                else "📞 التواصل للدعم" if current_language == "ar"
                                else "📞 Support Contact",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.TEAL_700
                            ),
                            ft.Text(
                                "Daha fazla yardıma mı ihtiyacınız var?\nBizimle iletişime geçin:" if current_language == "tr"
                                else "هل تحتاج إلى مزيد من المساعدة؟\nتواصل معنا:" if current_language == "ar"
                                else "Need more help?\nContact us:",
                                size=14,
                                color=ft.Colors.GREY_700,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                "📧 <EMAIL>\n🌐 www.mursid.com\n📱 +90 ************",
                                size=14,
                                color=ft.Colors.TEAL_600,
                                text_align=ft.TextAlign.CENTER,
                                weight=ft.FontWeight.BOLD
                            )
                        ], spacing=10),
                        padding=15,
                        bgcolor=ft.Colors.TEAL_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.TEAL_200)
                    ),

                    ft.Container(height=20),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_help()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    def show_settings(e):
        print("Settings clicked!")  # للتحقق

        # استخدام صفحة جديدة بدلاً من BottomSheet
        def close_settings():
            page.clean()
            main(page)

        def toggle_notifications(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Notifications: {status}"))
            page.snack_bar.open = True
            page.update()

        def toggle_auto_login(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Auto Login: {status}"))
            page.snack_bar.open = True
            page.update()

        def show_security_settings():
            page.snack_bar = ft.SnackBar(content=ft.Text("Security settings opening..."))
            page.snack_bar.open = True
            page.update()

        # مسح الصفحة الحالية وإظهار صفحة الإعدادات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_settings()
                        ),
                        ft.Text(
                            "Ayarlar" if current_language == "tr"
                            else "الإعدادات" if current_language == "ar"
                            else "Settings",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # الإعدادات
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.NOTIFICATIONS, color=ft.Colors.BLUE),
                        title=ft.Text(
                            "Bildirimler" if current_language == "tr"
                            else "الإشعارات" if current_language == "ar"
                            else "Notifications"
                        ),
                        subtitle=ft.Text(
                            "Bildirimleri aç/kapat" if current_language == "tr"
                            else "تشغيل/إيقاف الإشعارات" if current_language == "ar"
                            else "Turn notifications on/off"
                        ),
                        trailing=ft.Switch(value=True, on_change=toggle_notifications)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LOGIN, color=ft.Colors.GREEN),
                        title=ft.Text(
                            "Otomatik Giriş" if current_language == "tr"
                            else "الدخول التلقائي" if current_language == "ar"
                            else "Auto Login"
                        ),
                        subtitle=ft.Text(
                            "Otomatik giriş yap" if current_language == "tr"
                            else "تسجيل دخول تلقائي" if current_language == "ar"
                            else "Automatic login"
                        ),
                        trailing=ft.Switch(value=False, on_change=toggle_auto_login)
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SECURITY, color=ft.Colors.ORANGE),
                        title=ft.Text(
                            "Güvenlik" if current_language == "tr"
                            else "الأمان" if current_language == "ar"
                            else "Security"
                        ),
                        subtitle=ft.Text(
                            "Güvenlik ayarları" if current_language == "tr"
                            else "إعدادات الأمان" if current_language == "ar"
                            else "Security settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=lambda e: show_security_settings()
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_settings()
                    )

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_language_menu(e):
        print("Language menu clicked!")  # للتحقق

        # استخدام صفحة جديدة للغات
        def close_language():
            page.clean()
            main(page)

        def select_language(lang):
            print(f"Language selected: {lang}")  # للتحقق

            # تحديث اللغة في الصفحة
            page.current_language = lang
            print(f"Page language updated to: {page.current_language}")  # للتحقق

            # رسالة تأكيد تغيير اللغة
            page.snack_bar = ft.SnackBar(content=ft.Text(
                f"Dil değiştirildi: {texts[lang]['language_name']}" if lang == "tr"
                else f"تم تغيير اللغة: {texts[lang]['language_name']}" if lang == "ar"
                else f"Language changed: {texts[lang]['language_name']}"
            ))
            page.snack_bar.open = True
            page.update()

            # العودة الفورية للصفحة الرئيسية مع إعادة تحميل
            import time
            import threading
            def go_back():
                time.sleep(1.5)  # انتظار أقل
                print("Reloading page with new language...")  # للتحقق
                page.clean()
                main(page)  # إعادة تحميل الصفحة باللغة الجديدة
            threading.Thread(target=go_back).start()

        # مسح الصفحة الحالية وإظهار صفحة اللغات
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_language()
                        ),
                        ft.Text(
                            "Dil Seçimi" if current_language == "tr"
                            else "اختيار اللغة" if current_language == "ar"
                            else "Language Selection",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة اللغة
                    ft.Icon(ft.Icons.LANGUAGE, size=60, color=ft.Colors.BLUE_600),

                    ft.Text(
                        "Tercih ettiğiniz dili seçin" if current_language == "tr"
                        else "اختر اللغة المفضلة لديك" if current_language == "ar"
                        else "Choose your preferred language",
                        text_align=ft.TextAlign.CENTER,
                        size=14,
                        color=ft.Colors.GREY_600
                    ),

                    ft.Container(height=20),

                    # اللغة التركية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇹🇷", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "Türkçe",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Turkish • Türkiye",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "tr" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "tr" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("tr")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "tr" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "tr" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة العربية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇸🇦", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "العربية",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "Arabic • العربية",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "ar" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "ar" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("ar")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "ar" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "ar" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=10),

                    # اللغة الإنجليزية
                    ft.Container(
                        content=ft.ListTile(
                            leading=ft.Container(
                                content=ft.Text("🇺🇸", size=30),
                                width=50,
                                alignment=ft.alignment.center
                            ),
                            title=ft.Text(
                                "English",
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            subtitle=ft.Text(
                                "English • United States",
                                size=12,
                                color=ft.Colors.GREY_600
                            ),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if current_language == "en" else ft.Icons.RADIO_BUTTON_UNCHECKED,
                                color=ft.Colors.GREEN if current_language == "en" else ft.Colors.GREY_400
                            ),
                            on_click=lambda e: select_language("en")
                        ),
                        bgcolor=ft.Colors.GREEN_50 if current_language == "en" else ft.Colors.TRANSPARENT,
                        border_radius=10,
                        border=ft.border.all(2, ft.Colors.GREEN_300) if current_language == "en" else ft.border.all(1, ft.Colors.GREY_300),
                        padding=5
                    ),

                    ft.Container(height=30),

                    # معلومات إضافية
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "ℹ️ Bilgi" if current_language == "tr"
                                else "ℹ️ معلومة" if current_language == "ar"
                                else "ℹ️ Information",
                                size=14,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.BLUE_700
                            ),
                            ft.Text(
                                "Dil değişikliği anında uygulanır ve uygulama yeniden başlatılır." if current_language == "tr"
                                else "يتم تطبيق تغيير اللغة فوراً وإعادة تشغيل التطبيق." if current_language == "ar"
                                else "Language change is applied instantly and the app restarts.",
                                size=12,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], spacing=5),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=20),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_language()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_theme(e):
        print("Theme clicked!")  # للتحقق

        # استخدام صفحة جديدة للمظاهر
        def close_theme():
            page.clean()
            main(page)

        def change_theme(theme_mode, message):
            page.theme_mode = theme_mode
            page.snack_bar = ft.SnackBar(content=ft.Text(message))
            page.snack_bar.open = True
            page.update()
            # العودة للصفحة الرئيسية بعد ثانيتين
            import time
            import threading
            def go_back():
                time.sleep(2)
                close_theme()
            threading.Thread(target=go_back).start()

        def light_theme(e):
            change_theme(ft.ThemeMode.LIGHT,
                "Açık tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الفاتح" if current_language == "ar"
                else "Light theme selected")

        def dark_theme(e):
            change_theme(ft.ThemeMode.DARK,
                "Koyu tema seçildi" if current_language == "tr"
                else "تم اختيار المظهر الداكن" if current_language == "ar"
                else "Dark theme selected")

        def system_theme(e):
            change_theme(ft.ThemeMode.SYSTEM,
                "Sistem teması seçildi" if current_language == "tr"
                else "تم اختيار مظهر النظام" if current_language == "ar"
                else "System theme selected")

        # مسح الصفحة الحالية وإظهار صفحة المظاهر
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_theme()
                        ),
                        ft.Text(
                            "Tema Seçin" if current_language == "tr"
                            else "اختر المظهر" if current_language == "ar"
                            else "Choose Theme",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # خيارات المظاهر
                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.LIGHT_MODE, color=ft.Colors.ORANGE, size=30),
                        title=ft.Text(
                            "Açık Tema" if current_language == "tr"
                            else "المظهر الفاتح" if current_language == "ar"
                            else "Light Theme"
                        ),
                        subtitle=ft.Text(
                            "Beyaz arka plan" if current_language == "tr"
                            else "خلفية بيضاء" if current_language == "ar"
                            else "White background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=light_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.DARK_MODE, color=ft.Colors.BLUE_GREY, size=30),
                        title=ft.Text(
                            "Koyu Tema" if current_language == "tr"
                            else "المظهر الداكن" if current_language == "ar"
                            else "Dark Theme"
                        ),
                        subtitle=ft.Text(
                            "Siyah arka plan" if current_language == "tr"
                            else "خلفية سوداء" if current_language == "ar"
                            else "Black background"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=dark_theme
                    ),

                    ft.ListTile(
                        leading=ft.Icon(ft.Icons.SETTINGS_SYSTEM_DAYDREAM, color=ft.Colors.GREEN, size=30),
                        title=ft.Text(
                            "Sistem Teması" if current_language == "tr"
                            else "مظهر النظام" if current_language == "ar"
                            else "System Theme"
                        ),
                        subtitle=ft.Text(
                            "Sistem ayarlarını takip et" if current_language == "tr"
                            else "اتبع إعدادات النظام" if current_language == "ar"
                            else "Follow system settings"
                        ),
                        trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS),
                        on_click=system_theme
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_theme()
                    )

                ], spacing=15, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_privacy(e):
        print("Privacy clicked!")  # للتحقق

        # استخدام صفحة جديدة للخصوصية
        def close_privacy():
            page.clean()
            main(page)

        # مسح الصفحة الحالية وإظهار صفحة الخصوصية
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_privacy()
                        ),
                        ft.Text(
                            "Gizlilik" if current_language == "tr"
                            else "الخصوصية" if current_language == "ar"
                            else "Privacy",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة الخصوصية
                    ft.Icon(ft.Icons.PRIVACY_TIP, size=60, color=ft.Colors.GREEN_600),

                    ft.Text(
                        "Gizliliğiniz bizim için önemli" if current_language == "tr"
                        else "خصوصيتك مهمة بالنسبة لنا" if current_language == "ar"
                        else "Your privacy is important to us",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.GREEN_700
                    ),

                    ft.Container(height=20),

                    # معلومات الخصوصية
                    ft.Container(
                        content=ft.Text(
                            texts[current_language]["privacy_text"],
                            size=14,
                            text_align=ft.TextAlign.CENTER,
                            color=ft.Colors.GREY_700
                        ),
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.GREEN_200)
                    ),

                    ft.Container(height=30),

                    # زر الرجوع
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ARROW_BACK),
                            ft.Text(
                                "Geri Dön" if current_language == "tr"
                                else "العودة" if current_language == "ar"
                                else "Go Back"
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        width=200,
                        on_click=lambda e: close_privacy()
                    ),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=10, scroll=ft.ScrollMode.AUTO),
                padding=20,
                expand=True
            )
        )

    def show_admin_page():
        """عرض صفحة الأدمن"""
        print("Admin page opened!")  # للتحقق

        def close_admin():
            page.clean()
            main(page)

        def show_user_management(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Kullanıcı yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة المستخدمين..." if current_language == "ar"
                else "Opening user management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_system_settings(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Sistem ayarları açılıyor..." if current_language == "tr"
                else "فتح إعدادات النظام..." if current_language == "ar"
                else "Opening system settings..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_reports(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Raporlar açılıyor..." if current_language == "tr"
                else "فتح التقارير..." if current_language == "ar"
                else "Opening reports..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_system_logs(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Sistem günlükleri açılıyor..." if current_language == "tr"
                else "فتح سجلات النظام..." if current_language == "ar"
                else "Opening system logs..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_backup(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Yedekleme açılıyor..." if current_language == "tr"
                else "فتح النسخ الاحتياطي..." if current_language == "ar"
                else "Opening backup..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_admin_security(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Güvenlik ayarları açılıyor..." if current_language == "tr"
                else "فتح إعدادات الأمان..." if current_language == "ar"
                else "Opening security settings..."
            ))
            page.snack_bar.open = True
            page.update()

        def admin_logout(e):
            # تسجيل خروج المستخدم من قاعدة البيانات
            app_db.logout_user()

            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Çıkış yapılıyor..." if current_language == "tr"
                else "جاري تسجيل الخروج..." if current_language == "ar"
                else "Logging out..."
            ))
            page.snack_bar.open = True
            page.update()
            import time
            import threading
            def go_back():
                time.sleep(1.5)
                close_admin()
            threading.Thread(target=go_back).start()

        # وظائف شريط التنقل السفلي
        def show_main_dashboard(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Ana sayfa yükleniyor..." if current_language == "tr"
                else "تحميل الصفحة الرئيسية..." if current_language == "ar"
                else "Loading main dashboard..."
            ))
            page.snack_bar.open = True
            page.update()
            # إعادة تحميل صفحة الأدمن الرئيسية
            show_admin_page()

        def show_user_management_page(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Kullanıcı yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة المستخدمين..." if current_language == "ar"
                else "Opening user management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_employee_management(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Çalışan yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة الموظفين..." if current_language == "ar"
                else "Opening employee management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_reports_management(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Şikayet yönetimi açılıyor..." if current_language == "tr"
                else "فتح إدارة البلاغات..." if current_language == "ar"
                else "Opening reports management..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_fines_system(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Ceza sistemi açılıyor..." if current_language == "tr"
                else "فتح نظام الغرامات..." if current_language == "ar"
                else "Opening fines system..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_reports_page(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Raporlar açılıyor..." if current_language == "tr"
                else "فتح التقارير..." if current_language == "ar"
                else "Opening reports..."
            ))
            page.snack_bar.open = True
            page.update()

        def show_profile_page(e):
            page.snack_bar = ft.SnackBar(content=ft.Text(
                "Profil açılıyor..." if current_language == "tr"
                else "فتح البروفيل..." if current_language == "ar"
                else "Opening profile..."
            ))
            page.snack_bar.open = True
            page.update()

        # الحصول على الإحصائيات الحقيقية من قاعدة البيانات
        stats = app_db.get_system_stats()
        dashboard_stats = app_db.get_dashboard_stats()
        current_user = app_db.get_current_user()

        # مسح الصفحة الحالية وإظهار صفحة الأدمن
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الخروج
                    ft.Row([
                        ft.Text(
                            texts[current_language]["admin_dashboard"],
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_700
                        ),
                        ft.Container(expand=True),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.LOGOUT, size=16),
                                ft.Text(texts[current_language]["admin_logout"])
                            ], spacing=5),
                            on_click=admin_logout,
                            style=ft.ButtonStyle(bgcolor=ft.Colors.RED_600, color=ft.Colors.WHITE)
                        )
                    ]),

                    ft.Divider(),

                    # رسالة الترحيب
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.ADMIN_PANEL_SETTINGS, size=40, color=ft.Colors.BLUE_600),
                            ft.Column([
                                ft.Text(
                                    texts[current_language]["admin_welcome"],
                                    size=20,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.BLUE_700
                                ),
                                ft.Text(
                                    current_user['email'] if current_user else "<EMAIL>",
                                    size=14,
                                    color=ft.Colors.GREY_600
                                )
                            ], spacing=2, expand=True)
                        ]),
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),

                    ft.Container(height=20),

                    # لوحة البيانات التفاعلية
                    ft.Text(
                        "📊 " + ("İstatistikler" if current_language == "tr"
                        else "الإحصائيات" if current_language == "ar"
                        else "Statistics"),
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_700
                    ),

                    # الصف الأول - المستخدمين والموظفين
                    ft.Row([
                        # عدد المستخدمين
                        ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.PEOPLE, size=30, color=ft.Colors.BLUE_600),
                                    ft.Column([
                                        ft.Text(str(dashboard_stats.get('total_users', 0)), size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                                        ft.Text(
                                            "المستخدمين" if current_language == "ar"
                                            else "Kullanıcılar" if current_language == "tr"
                                            else "Users",
                                            size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700
                                        )
                                    ], spacing=2, expand=True)
                                ], alignment=ft.MainAxisAlignment.CENTER),
                                ft.Divider(height=1, color=ft.Colors.BLUE_200),
                                ft.Text(
                                    "المسجلين في النظام" if current_language == "ar"
                                    else "Sistemde kayıtlı" if current_language == "tr"
                                    else "Registered in system",
                                    size=12, color=ft.Colors.BLUE_500, text_align=ft.TextAlign.CENTER
                                )
                            ], spacing=8),
                            padding=15,
                            bgcolor=ft.Colors.BLUE_50,
                            border_radius=12,
                            border=ft.border.all(2, ft.Colors.BLUE_200),
                            expand=True,
                            shadow=ft.BoxShadow(
                                spread_radius=1,
                                blur_radius=3,
                                color=ft.Colors.BLUE_100,
                                offset=ft.Offset(0, 2)
                            )
                        ),
                        ft.Container(width=10),

                        # عدد الموظفين
                        ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.BADGE, size=30, color=ft.Colors.GREEN_600),
                                    ft.Column([
                                        ft.Text(str(dashboard_stats.get('total_employees', 0)), size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                        ft.Text(
                                            "الموظفين" if current_language == "ar"
                                            else "Çalışanlar" if current_language == "tr"
                                            else "Employees",
                                            size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700
                                        )
                                    ], spacing=2, expand=True)
                                ], alignment=ft.MainAxisAlignment.CENTER),
                                ft.Divider(height=1, color=ft.Colors.GREEN_200),
                                ft.Text(
                                    "العاملين النشطين" if current_language == "ar"
                                    else "Aktif çalışanlar" if current_language == "tr"
                                    else "Active employees",
                                    size=12, color=ft.Colors.GREEN_500, text_align=ft.TextAlign.CENTER
                                )
                            ], spacing=8),
                            padding=15,
                            bgcolor=ft.Colors.GREEN_50,
                            border_radius=12,
                            border=ft.border.all(2, ft.Colors.GREEN_200),
                            expand=True,
                            shadow=ft.BoxShadow(
                                spread_radius=1,
                                blur_radius=3,
                                color=ft.Colors.GREEN_100,
                                offset=ft.Offset(0, 2)
                            )
                        )
                    ]),

                    ft.Container(height=10),

                    # الصف الثاني - البلاغات والغرامات
                    ft.Row([
                        # عدد البلاغات
                        ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.REPORT_PROBLEM, size=30, color=ft.Colors.RED_600),
                                    ft.Column([
                                        ft.Text(str(dashboard_stats.get('total_reports', 0)), size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600),
                                        ft.Text(
                                            "البلاغات" if current_language == "ar"
                                            else "Şikayetler" if current_language == "tr"
                                            else "Reports",
                                            size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_700
                                        )
                                    ], spacing=2, expand=True)
                                ], alignment=ft.MainAxisAlignment.CENTER),
                                ft.Divider(height=1, color=ft.Colors.RED_200),
                                ft.Row([
                                    ft.Column([
                                        ft.Text(str(dashboard_stats.get('pending_reports', 0)), size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600),
                                        ft.Text("معلقة" if current_language == "ar" else "Bekleyen" if current_language == "tr" else "Pending", size=10, color=ft.Colors.ORANGE_600)
                                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                                    ft.Column([
                                        ft.Text(str(dashboard_stats.get('resolved_reports', 0)), size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                        ft.Text("محلولة" if current_language == "ar" else "Çözülen" if current_language == "tr" else "Resolved", size=10, color=ft.Colors.GREEN_600)
                                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
                                ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                            ], spacing=8),
                            padding=15,
                            bgcolor=ft.Colors.RED_50,
                            border_radius=12,
                            border=ft.border.all(2, ft.Colors.RED_200),
                            expand=True,
                            shadow=ft.BoxShadow(
                                spread_radius=1,
                                blur_radius=3,
                                color=ft.Colors.RED_100,
                                offset=ft.Offset(0, 2)
                            )
                        ),
                        ft.Container(width=10),

                        # إجمالي الغرامات
                        ft.Container(
                            content=ft.Column([
                                ft.Row([
                                    ft.Icon(ft.Icons.GAVEL, size=30, color=ft.Colors.PURPLE_600),
                                    ft.Column([
                                        ft.Text(
                                            f"{dashboard_stats.get('total_fines_amount', 0):,.0f} ₺",
                                            size=28, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600
                                        ),
                                        ft.Text(
                                            "إجمالي الغرامات" if current_language == "ar"
                                            else "Toplam Cezalar" if current_language == "tr"
                                            else "Total Fines",
                                            size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700
                                        )
                                    ], spacing=2, expand=True)
                                ], alignment=ft.MainAxisAlignment.CENTER),
                                ft.Divider(height=1, color=ft.Colors.PURPLE_200),
                                ft.Row([
                                    ft.Column([
                                        ft.Text(f"{dashboard_stats.get('paid_fines_amount', 0):,.0f} ₺", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                        ft.Text("مدفوعة" if current_language == "ar" else "Ödenen" if current_language == "tr" else "Paid", size=10, color=ft.Colors.GREEN_600)
                                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                                    ft.Column([
                                        ft.Text(f"{dashboard_stats.get('pending_fines_amount', 0):,.0f} ₺", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600),
                                        ft.Text("معلقة" if current_language == "ar" else "Bekleyen" if current_language == "tr" else "Pending", size=10, color=ft.Colors.ORANGE_600)
                                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
                                ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                            ], spacing=8),
                            padding=15,
                            bgcolor=ft.Colors.PURPLE_50,
                            border_radius=12,
                            border=ft.border.all(2, ft.Colors.PURPLE_200),
                            expand=True,
                            shadow=ft.BoxShadow(
                                spread_radius=1,
                                blur_radius=3,
                                color=ft.Colors.PURPLE_100,
                                offset=ft.Offset(0, 2)
                            )
                        )
                    ]),

                    ft.Container(height=10),

                    # الصف الثالث - مجموع ما حصل عليه المواطنون
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.ACCOUNT_BALANCE_WALLET, size=35, color=ft.Colors.TEAL_600),
                                ft.Column([
                                    ft.Text(
                                        f"{dashboard_stats.get('total_collections_amount', 0):,.0f} ₺",
                                        size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.TEAL_600
                                    ),
                                    ft.Text(
                                        "مجموع ما حُصل من المواطنين" if current_language == "ar"
                                        else "Vatandaşlardan Toplanan" if current_language == "tr"
                                        else "Total Collected from Citizens",
                                        size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.TEAL_700
                                    )
                                ], spacing=2, expand=True)
                            ], alignment=ft.MainAxisAlignment.CENTER),
                            ft.Divider(height=1, color=ft.Colors.TEAL_200),
                            ft.Row([
                                ft.Column([
                                    ft.Text(f"{dashboard_stats.get('license_fees_amount', 0):,.0f} ₺", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                                    ft.Text("رسوم تراخيص" if current_language == "ar" else "Lisans Ücretleri" if current_language == "tr" else "License Fees", size=11, color=ft.Colors.BLUE_600)
                                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                                ft.Container(
                                    content=ft.VerticalDivider(width=1, color=ft.Colors.TEAL_300),
                                    height=40
                                ),
                                ft.Column([
                                    ft.Text(f"{dashboard_stats.get('service_fees_amount', 0):,.0f} ₺", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                                    ft.Text("رسوم خدمات" if current_language == "ar" else "Hizmet Ücretleri" if current_language == "tr" else "Service Fees", size=11, color=ft.Colors.GREEN_600)
                                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                                ft.Container(
                                    content=ft.VerticalDivider(width=1, color=ft.Colors.TEAL_300),
                                    height=40
                                ),
                                ft.Column([
                                    ft.Text(f"{dashboard_stats.get('local_taxes_amount', 0):,.0f} ₺", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600),
                                    ft.Text("ضرائب محلية" if current_language == "ar" else "Yerel Vergiler" if current_language == "tr" else "Local Taxes", size=11, color=ft.Colors.ORANGE_600)
                                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                                ft.Container(
                                    content=ft.VerticalDivider(width=1, color=ft.Colors.TEAL_300),
                                    height=40
                                ),
                                ft.Column([
                                    ft.Text(str(dashboard_stats.get('total_collections_count', 0)), size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.TEAL_600),
                                    ft.Text("إجمالي العمليات" if current_language == "ar" else "Toplam İşlem" if current_language == "tr" else "Total Operations", size=11, color=ft.Colors.TEAL_600)
                                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
                            ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                        ], spacing=10),
                        padding=20,
                        bgcolor=ft.Colors.TEAL_50,
                        border_radius=12,
                        border=ft.border.all(2, ft.Colors.TEAL_200),
                        shadow=ft.BoxShadow(
                            spread_radius=1,
                            blur_radius=5,
                            color=ft.Colors.TEAL_100,
                            offset=ft.Offset(0, 3)
                        )
                    ),

                    ft.Container(height=50)

                ], spacing=10, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            ),

            # شريط التنقل السفلي
            ft.Container(
                content=ft.Row([
                    # الصفحة الرئيسية
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.HOME,
                                icon_color=ft.Colors.BLUE_600,
                                icon_size=24,
                                on_click=show_main_dashboard,
                                tooltip="الصفحة الرئيسية" if current_language == "ar" else "Ana Sayfa" if current_language == "tr" else "Home"
                            ),
                            ft.Text(
                                "الرئيسية" if current_language == "ar" else "Ana Sayfa" if current_language == "tr" else "Home",
                                size=10,
                                color=ft.Colors.BLUE_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # إدارة المستخدمين
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.PEOPLE,
                                icon_color=ft.Colors.GREEN_600,
                                icon_size=24,
                                on_click=show_user_management_page,
                                tooltip="إدارة المستخدمين" if current_language == "ar" else "Kullanıcı Yönetimi" if current_language == "tr" else "User Management"
                            ),
                            ft.Text(
                                "المستخدمين" if current_language == "ar" else "Kullanıcılar" if current_language == "tr" else "Users",
                                size=10,
                                color=ft.Colors.GREEN_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # إدارة الموظفين
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.BADGE,
                                icon_color=ft.Colors.ORANGE_600,
                                icon_size=24,
                                on_click=show_employee_management,
                                tooltip="إدارة الموظفين" if current_language == "ar" else "Çalışan Yönetimi" if current_language == "tr" else "Employee Management"
                            ),
                            ft.Text(
                                "الموظفين" if current_language == "ar" else "Çalışanlar" if current_language == "tr" else "Employees",
                                size=10,
                                color=ft.Colors.ORANGE_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # إدارة البلاغات
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.REPORT_PROBLEM,
                                icon_color=ft.Colors.RED_600,
                                icon_size=24,
                                on_click=show_reports_management,
                                tooltip="إدارة البلاغات" if current_language == "ar" else "Şikayet Yönetimi" if current_language == "tr" else "Reports Management"
                            ),
                            ft.Text(
                                "البلاغات" if current_language == "ar" else "Şikayetler" if current_language == "tr" else "Reports",
                                size=10,
                                color=ft.Colors.RED_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # نظام الغرامات
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.GAVEL,
                                icon_color=ft.Colors.PURPLE_600,
                                icon_size=24,
                                on_click=show_fines_system,
                                tooltip="نظام الغرامات" if current_language == "ar" else "Ceza Sistemi" if current_language == "tr" else "Fines System"
                            ),
                            ft.Text(
                                "الغرامات" if current_language == "ar" else "Cezalar" if current_language == "tr" else "Fines",
                                size=10,
                                color=ft.Colors.PURPLE_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # التقارير
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.ASSESSMENT,
                                icon_color=ft.Colors.TEAL_600,
                                icon_size=24,
                                on_click=show_reports_page,
                                tooltip="التقارير" if current_language == "ar" else "Raporlar" if current_language == "tr" else "Reports"
                            ),
                            ft.Text(
                                "التقارير" if current_language == "ar" else "Raporlar" if current_language == "tr" else "Reports",
                                size=10,
                                color=ft.Colors.TEAL_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                    # البروفيل
                    ft.Container(
                        content=ft.Column([
                            ft.IconButton(
                                icon=ft.Icons.ACCOUNT_CIRCLE,
                                icon_color=ft.Colors.INDIGO_600,
                                icon_size=24,
                                on_click=show_profile_page,
                                tooltip="البروفيل" if current_language == "ar" else "Profil" if current_language == "tr" else "Profile"
                            ),
                            ft.Text(
                                "البروفيل" if current_language == "ar" else "Profil" if current_language == "tr" else "Profile",
                                size=10,
                                color=ft.Colors.INDIGO_600,
                                text_align=ft.TextAlign.CENTER
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2),
                        expand=True
                    ),

                ], alignment=ft.MainAxisAlignment.SPACE_EVENLY),
                padding=ft.padding.symmetric(horizontal=10, vertical=8),
                bgcolor=ft.Colors.WHITE,
                border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_300)),
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=5,
                    color=ft.Colors.GREY_400,
                    offset=ft.Offset(0, -2)
                )
            )
        )

    def login_clicked(e):
        # Veri doğrulama
        if not email_field.value:
            email_field.error_text = texts[current_language]["email_error"]
            page.update()
            return

        if not password_field.value:
            password_field.error_text = texts[current_language]["password_error"]
            page.update()
            return

        # Hata mesajlarını temizle
        email_field.error_text = ""
        password_field.error_text = ""

        # محاولة تسجيل الدخول باستخدام قاعدة البيانات
        success, message, user_data = app_db.authenticate_user(email_field.value, password_field.value)

        if success:
            # نجح تسجيل الدخول
            success_message.value = f"{texts[current_language]['success_message']}{user_data['email']}"
            success_message.visible = True
            login_button.disabled = True
            page.update()

            # انتظار قصير ثم التحقق من نوع المستخدم
            import time
            import threading
            def go_to_next():
                time.sleep(1.5)
                if user_data.get('is_admin', False):
                    # مدير - الانتقال لصفحة الأدمن
                    show_admin_page()
                else:
                    # مستخدم عادي - يمكن إضافة صفحة المستخدم هنا
                    success_message.value = f"مرحباً {user_data['first_name']} {user_data['last_name']}!"
                    success_message.visible = True
                    login_button.disabled = False
                    page.update()
            threading.Thread(target=go_to_next).start()
        else:
            # فشل تسجيل الدخول
            page.snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.RED_400
            )
            page.snack_bar.open = True
            page.update()

    def register_clicked(e):
        print("Register clicked!")  # للتحقق
        show_register_page()

    def show_register_page():
        """عرض صفحة التسجيل"""
        print("Register page opened!")  # للتحقق

        def close_register():
            page.clean()
            main(page)

        def validate_and_register(e):
            # جمع البيانات من الحقول
            registration_data = {
                'first_name': first_name_field.value,
                'last_name': last_name_field.value,
                'kimlik_number': kimlik_field.value,
                'phone_number': phone_field.value,
                'birth_date': birth_date_field.value,
                'address': address_field.value,
                'email': email_reg_field.value,
                'password': password_reg_field.value,
                'confirm_password': confirm_password_field.value,
            }

            # التحقق من صحة البيانات باستخدام قاعدة البيانات
            is_valid, message = app_db.validate_registration_data(registration_data)

            if not is_valid:
                # عرض رسالة الخطأ
                page.snack_bar = ft.SnackBar(
                    content=ft.Text(message),
                    bgcolor=ft.Colors.RED_400
                )
                page.snack_bar.open = True
                page.update()
                return

            # محاولة تسجيل المستخدم في قاعدة البيانات
            success, message = app_db.register_user(registration_data)

            if success:
                # نجح التسجيل
                page.snack_bar = ft.SnackBar(
                    content=ft.Text(
                        "تم إنشاء الحساب بنجاح!" if current_language == "ar"
                        else "Hesap başarıyla oluşturuldu!" if current_language == "tr"
                        else "Account created successfully!"
                    ),
                    bgcolor=ft.Colors.GREEN_400
                )
                page.snack_bar.open = True
                page.update()

                # العودة لصفحة تسجيل الدخول بعد ثانيتين
                import time
                import threading
                def go_back():
                    time.sleep(2)
                    close_register()
                threading.Thread(target=go_back).start()
            else:
                # فشل التسجيل
                page.snack_bar = ft.SnackBar(
                    content=ft.Text(message),
                    bgcolor=ft.Colors.RED_400
                )
                page.snack_bar.open = True
                page.update()

        # حقول الإدخال - جميعها إجبارية
        first_name_field = ft.TextField(
            label="* الاسم" if current_language == "ar" else "* Ad" if current_language == "tr" else "* First Name",
            hint_text="أدخل اسمك الأول (إجباري)" if current_language == "ar" else "Adınızı girin (zorunlu)" if current_language == "tr" else "Enter your first name (required)",
            prefix_icon=ft.Icons.PERSON,
            expand=True,
            border_color=ft.Colors.RED_300  # لون أحمر للحقول الإجبارية
        )

        last_name_field = ft.TextField(
            label="* اسم العائلة" if current_language == "ar" else "* Soyadı" if current_language == "tr" else "* Last Name",
            hint_text="أدخل اسم العائلة (إجباري)" if current_language == "ar" else "Soyadınızı girin (zorunlu)" if current_language == "tr" else "Enter your last name (required)",
            prefix_icon=ft.Icons.FAMILY_RESTROOM,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        kimlik_field = ft.TextField(
            label="* رقم الكيمليك" if current_language == "ar" else "* Kimlik Numarası" if current_language == "tr" else "* ID Number",
            hint_text="11 رقم (إجباري)" if current_language == "ar" else "11 haneli (zorunlu)" if current_language == "tr" else "11 digits (required)",
            prefix_icon=ft.Icons.BADGE,
            keyboard_type=ft.KeyboardType.NUMBER,
            max_length=11,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        phone_field = ft.TextField(
            label="* رقم الهاتف" if current_language == "ar" else "* Telefon Numarası" if current_language == "tr" else "* Phone Number",
            hint_text="+90 ************ (إجباري)" if current_language == "ar" else "+90 ************ (zorunlu)" if current_language == "tr" else "+90 ************ (required)",
            prefix_icon=ft.Icons.PHONE,
            keyboard_type=ft.KeyboardType.PHONE,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        birth_date_field = ft.TextField(
            label="* تاريخ الميلاد" if current_language == "ar" else "* Doğum Tarihi" if current_language == "tr" else "* Birth Date",
            hint_text="DD/MM/YYYY (إجباري)" if current_language == "ar" else "GG/AA/YYYY (zorunlu)" if current_language == "tr" else "DD/MM/YYYY (required)",
            prefix_icon=ft.Icons.CALENDAR_TODAY,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        address_field = ft.TextField(
            label="* العنوان" if current_language == "ar" else "* Adres" if current_language == "tr" else "* Address",
            hint_text="أدخل عنوانك الكامل (إجباري)" if current_language == "ar" else "Tam adresinizi girin (zorunlu)" if current_language == "tr" else "Enter your full address (required)",
            prefix_icon=ft.Icons.HOME,
            multiline=True,
            min_lines=2,
            max_lines=3,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        email_reg_field = ft.TextField(
            label="* البريد الإلكتروني" if current_language == "ar" else "* E-posta" if current_language == "tr" else "* Email",
            hint_text="<EMAIL> (إجباري)" if current_language == "ar" else "<EMAIL> (zorunlu)" if current_language == "tr" else "<EMAIL> (required)",
            prefix_icon=ft.Icons.EMAIL,
            keyboard_type=ft.KeyboardType.EMAIL,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        password_reg_field = ft.TextField(
            label="* كلمة المرور" if current_language == "ar" else "* Şifre" if current_language == "tr" else "* Password",
            hint_text="على الأقل 6 أحرف (إجباري)" if current_language == "ar" else "En az 6 karakter (zorunlu)" if current_language == "tr" else "At least 6 characters (required)",
            prefix_icon=ft.Icons.LOCK,
            password=True,
            can_reveal_password=True,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        confirm_password_field = ft.TextField(
            label="* إعادة كلمة المرور" if current_language == "ar" else "* Şifre Tekrarı" if current_language == "tr" else "* Confirm Password",
            hint_text="أعد إدخال كلمة المرور (إجباري)" if current_language == "ar" else "Şifreyi tekrar girin (zorunlu)" if current_language == "tr" else "Re-enter password (required)",
            prefix_icon=ft.Icons.LOCK_OUTLINE,
            password=True,
            can_reveal_password=True,
            expand=True,
            border_color=ft.Colors.RED_300
        )

        # أزرار الصفحة
        register_submit_button = ft.ElevatedButton(
            text="إنشاء الحساب" if current_language == "ar" else "Hesap Oluştur" if current_language == "tr" else "Create Account",
            width=250,
            height=45,
            on_click=validate_and_register,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.GREEN_600,
            )
        )

        back_button = ft.TextButton(
            text="العودة" if current_language == "ar" else "Geri" if current_language == "tr" else "Back",
            on_click=lambda e: close_register(),
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600,
            )
        )

        # مسح الصفحة الحالية وإظهار صفحة التسجيل
        page.clean()

        page.add(
            ft.Container(
                content=ft.Column([
                    # شريط علوي مع زر الرجوع
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK,
                            on_click=lambda e: close_register()
                        ),
                        ft.Text(
                            "حساب جديد" if current_language == "ar"
                            else "Yeni Hesap" if current_language == "tr"
                            else "New Account",
                            size=20, weight=ft.FontWeight.BOLD
                        ),
                        ft.Container(expand=True),
                    ]),

                    ft.Divider(),

                    # أيقونة التسجيل
                    ft.Icon(ft.Icons.PERSON_ADD, size=50, color=ft.Colors.GREEN_600),

                    ft.Text(
                        "إنشاء حساب جديد" if current_language == "ar"
                        else "Yeni hesap oluşturun" if current_language == "tr"
                        else "Create a new account",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.GREEN_700
                    ),

                    ft.Container(height=10),

                    # حقول الإدخال في مجموعات
                    ft.ResponsiveRow([
                        ft.Column([
                            # المعلومات الشخصية
                            ft.Text("المعلومات الشخصية" if current_language == "ar"
                                   else "Kişisel Bilgiler" if current_language == "tr"
                                   else "Personal Information",
                                   size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            first_name_field,
                            ft.Container(height=5),
                            last_name_field,
                            ft.Container(height=5),
                            kimlik_field,
                            ft.Container(height=5),
                            birth_date_field,

                            ft.Container(height=15),

                            # معلومات الاتصال
                            ft.Text("معلومات الاتصال" if current_language == "ar"
                                   else "İletişim Bilgileri" if current_language == "tr"
                                   else "Contact Information",
                                   size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            phone_field,
                            ft.Container(height=5),
                            email_reg_field,
                            ft.Container(height=5),
                            address_field,

                            ft.Container(height=15),

                            # معلومات الحساب
                            ft.Text("معلومات الحساب" if current_language == "ar"
                                   else "Hesap Bilgileri" if current_language == "tr"
                                   else "Account Information",
                                   size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            password_reg_field,
                            ft.Container(height=5),
                            confirm_password_field,

                        ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                    ], alignment=ft.MainAxisAlignment.CENTER),

                    ft.Container(height=20),

                    # الأزرار
                    ft.Column([
                        register_submit_button,
                        ft.Container(height=5),
                        back_button,
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),

                    # مساحة إضافية في الأسفل
                    ft.Container(height=30)

                ], spacing=8, scroll=ft.ScrollMode.ALWAYS),
                padding=20,
                expand=True
            )
        )

    def forgot_password_clicked(e):
        page.snack_bar = ft.SnackBar(content=ft.Text(texts[current_language]["forgot_text"]))
        page.snack_bar.open = True
        page.update()

    # Giriş alanları - responsive
    email_field = ft.TextField(
        label=texts[current_language]["email_label"],
        hint_text=texts[current_language]["email_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.EMAIL,
        keyboard_type=ft.KeyboardType.EMAIL,
        autofocus=True
    )

    password_field = ft.TextField(
        label=texts[current_language]["password_label"],
        hint_text=texts[current_language]["password_hint"],
        expand=True,  # Mevcut alanı doldur
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Uygulama düğmeleri - responsive
    login_button = ft.ElevatedButton(
        text=texts[current_language]["login_button"],
        width=250,  # عرض ثابت للزر
        height=45,
        on_click=login_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE_600,
        )
    )

    register_button = ft.TextButton(
        text=texts[current_language]["register_button"],
        on_click=register_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.BLUE_600,  # نص أزرق
        )
    )

    forgot_password_button = ft.TextButton(
        text=texts[current_language]["forgot_button"],
        on_click=forgot_password_clicked,
        style=ft.ButtonStyle(
            color=ft.Colors.BLUE_600,  # نص أزرق
        )
    )

    # رسالة النجاح
    success_message = ft.Text(
        value="",
        color=ft.Colors.GREEN_600,
        size=16,
        text_align=ft.TextAlign.CENTER,
        visible=False
    )



    # تخطيط الصفحة - responsive
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات في الأعلى
                ft.Row([
                    ft.Container(expand=True),  # مساحة فارغة لدفع القائمة لليمين
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,  # أيقونة الثلاث نقاط
                        items=[
                            ft.PopupMenuItem(
                                text=texts[current_language]["about"],
                                icon=ft.Icons.INFO,
                                on_click=show_about
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["help"],
                                icon=ft.Icons.HELP,
                                on_click=show_help
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["settings"],
                                icon=ft.Icons.SETTINGS,
                                on_click=show_settings
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["language"],
                                icon=ft.Icons.LANGUAGE,
                                on_click=show_language_menu
                            ),
                            ft.PopupMenuItem(
                                text=texts[current_language]["theme"],
                                icon=ft.Icons.PALETTE,
                                on_click=show_theme
                            ),
                            ft.PopupMenuItem(),  # Ayırıcı çizgi
                            ft.PopupMenuItem(
                                text=texts[current_language]["privacy"],
                                icon=ft.Icons.PRIVACY_TIP,
                                on_click=show_privacy
                            ),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),

                # العنوان والشعار - responsive
                ft.Text(
                    "🛡️",
                    size=60,  # حجم كبير للوجو
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Mürşid",
                    size=24,  # حجم كبير لاسم التطبيق
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_600,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["login_title"],
                    size=18,  # Alt başlık için küçük boyut
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    texts[current_language]["welcome"],
                    size=14,  # Orta boyut
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                ),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # حقول الإدخال - responsive
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=8),
                        password_field,
                    ], col={"sm": 12, "md": 10, "lg": 8}, alignment=ft.MainAxisAlignment.CENTER)
                ], alignment=ft.MainAxisAlignment.CENTER),

                # مساحة فارغة
                ft.Container(height=10),  # مساحة صغيرة جداً

                # جميع الأزرار في المنتصف - responsive
                ft.Column([
                    login_button,
                    ft.Container(height=15),  # مساحة أكبر بعد زر تسجيل الدخول
                    register_button,
                    forgot_password_button,  # بدون مساحة - ملتصقين
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),

                # رسالة النجاح
                success_message,

            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,  # مساحة متكيفة
            tight=True  # يتكيف مع المحتوى
            ),
            padding=ft.padding.symmetric(horizontal=20, vertical=20),  # حشو متكيف
            margin=ft.margin.only(left=10, right=10, top=10, bottom=10),  # هامش خارجي
            border_radius=10,
            bgcolor=ft.Colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.BLUE_GREY_300,
                offset=ft.Offset(0, 0),
            ),
            expand=True,  # يتوسع ليملأ المساحة المتاحة
            width=None,  # عرض متكيف
            height=None  # ارتفاع متكيف
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
