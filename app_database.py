#!/usr/bin/env python3
"""
تكامل قاعدة البيانات مع التطبيق - Mürşid App Database Integration
دمج قاعدة البيانات مع واجهة المستخدم
"""

import flet as ft
from typing import Optional, Dict, Any
from database import db_manager
from security import security_manager
from user_manager import user_manager
from models import RegistrationData, LoginData, SystemConstants
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AppDatabaseManager:
    """مدير قاعدة البيانات للتطبيق"""

    def __init__(self):
        """تهيئة مدير قاعدة البيانات للتطبيق"""
        self.db = db_manager
        self.security = security_manager
        self.user_manager = user_manager
        self.current_user = None
        self.current_session = None

    def authenticate_user(self, email: str, password: str) -> tuple[bool, str, Optional[Dict]]:
        """
        تسجيل دخول المستخدم

        Args:
            email: البريد الإلكتروني
            password: كلمة المرور

        Returns:
            tuple[bool, str, Optional[Dict]]: (نجح أم لا, رسالة, بيانات المستخدم)
        """
        try:
            # التحقق من البيانات
            login_data = LoginData(email=email, password=password)
            is_valid, message = login_data.validate()

            if not is_valid:
                return False, message, None

            # محاولة تسجيل الدخول
            success, message, user_data = self.user_manager.authenticate_user(email, password)

            if success:
                self.current_user = user_data
                self.current_session = user_data.get('session_token')
                logger.info(f"تم تسجيل دخول المستخدم: {email}")

            return success, message, user_data

        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            return False, "حدث خطأ في تسجيل الدخول", None

    def register_user(self, registration_data: Dict[str, Any]) -> tuple[bool, str]:
        """
        تسجيل مستخدم جديد

        Args:
            registration_data: بيانات التسجيل

        Returns:
            tuple[bool, str]: (نجح أم لا, رسالة)
        """
        try:
            # إنشاء نموذج بيانات التسجيل
            reg_data = RegistrationData(**registration_data)

            # التحقق من صحة البيانات
            is_valid, message = reg_data.validate()
            if not is_valid:
                return False, message

            # تحويل إلى قاموس المستخدم
            user_dict = reg_data.to_user_dict()

            # محاولة تسجيل المستخدم
            success, message, user_id = self.user_manager.register_user(user_dict)

            return success, message

        except Exception as e:
            logger.error(f"خطأ في تسجيل المستخدم: {e}")
            return False, "حدث خطأ في إنشاء الحساب"

    def logout_user(self) -> bool:
        """
        تسجيل خروج المستخدم الحالي

        Returns:
            bool: True إذا تم تسجيل الخروج بنجاح
        """
        try:
            if self.current_session:
                success = self.user_manager.logout_user(self.current_session)
                if success:
                    self.current_user = None
                    self.current_session = None
                    logger.info("تم تسجيل خروج المستخدم")
                return success
            return True

        except Exception as e:
            logger.error(f"خطأ في تسجيل الخروج: {e}")
            return False

    def is_user_logged_in(self) -> bool:
        """
        التحقق من تسجيل دخول المستخدم

        Returns:
            bool: True إذا كان المستخدم مسجل الدخول
        """
        if not self.current_session:
            return False

        # التحقق من صحة الجلسة
        user_data = self.user_manager.validate_session(self.current_session)
        if user_data:
            self.current_user = user_data
            return True
        else:
            self.current_user = None
            self.current_session = None
            return False

    def is_admin(self) -> bool:
        """
        التحقق من صلاحيات المدير

        Returns:
            bool: True إذا كان المستخدم مدير
        """
        return (self.current_user and
                self.current_user.get('is_admin', False))

    def get_current_user(self) -> Optional[Dict]:
        """
        الحصول على بيانات المستخدم الحالي

        Returns:
            Optional[Dict]: بيانات المستخدم أو None
        """
        return self.current_user

    def get_all_users(self) -> list[Dict]:
        """
        الحصول على جميع المستخدمين (للمديرين فقط)

        Returns:
            list[Dict]: قائمة المستخدمين
        """
        if not self.is_admin():
            return []

        return self.user_manager.get_all_users()

    def get_system_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات النظام

        Returns:
            Dict[str, Any]: إحصائيات النظام
        """
        return self.db.get_database_stats()

    def get_dashboard_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات لوحة التحكم

        Returns:
            Dict[str, Any]: إحصائيات لوحة التحكم
        """
        return self.db.get_dashboard_stats()

    def update_user_profile(self, update_data: Dict[str, Any]) -> tuple[bool, str]:
        """
        تحديث ملف المستخدم الحالي

        Args:
            update_data: البيانات المحدثة

        Returns:
            tuple[bool, str]: (نجح أم لا, رسالة)
        """
        if not self.current_user:
            return False, "يجب تسجيل الدخول أولاً"

        user_id = self.current_user.get('id')
        if not user_id:
            return False, "معرف المستخدم غير صحيح"

        return self.user_manager.update_user_profile(user_id, update_data)

    def change_password(self, old_password: str, new_password: str) -> tuple[bool, str]:
        """
        تغيير كلمة مرور المستخدم الحالي

        Args:
            old_password: كلمة المرور القديمة
            new_password: كلمة المرور الجديدة

        Returns:
            tuple[bool, str]: (نجح أم لا, رسالة)
        """
        if not self.current_user:
            return False, "يجب تسجيل الدخول أولاً"

        user_id = self.current_user.get('id')
        if not user_id:
            return False, "معرف المستخدم غير صحيح"

        success, message = self.user_manager.change_password(user_id, old_password, new_password)

        # إذا تم تغيير كلمة المرور بنجاح، قم بتسجيل الخروج
        if success:
            self.logout_user()

        return success, message

    def deactivate_user(self, user_id: int) -> tuple[bool, str]:
        """
        إلغاء تفعيل مستخدم (للمديرين فقط)

        Args:
            user_id: معرف المستخدم

        Returns:
            tuple[bool, str]: (نجح أم لا, رسالة)
        """
        if not self.is_admin():
            return False, "ليس لديك صلاحية لهذا الإجراء"

        admin_id = self.current_user.get('id')
        return self.user_manager.deactivate_user(user_id, admin_id)

    def validate_registration_data(self, data: Dict[str, Any]) -> tuple[bool, str]:
        """
        التحقق من صحة بيانات التسجيل

        Args:
            data: بيانات التسجيل

        Returns:
            tuple[bool, str]: (صحيحة أم لا, رسالة الخطأ)
        """
        try:
            # التحقق من الحقول المطلوبة
            required_fields = [
                'first_name', 'last_name', 'kimlik_number',
                'phone_number', 'birth_date', 'address',
                'email', 'password', 'confirm_password'
            ]

            for field in required_fields:
                if not data.get(field, '').strip():
                    field_names = {
                        'first_name': 'الاسم الأول',
                        'last_name': 'اسم العائلة',
                        'kimlik_number': 'رقم الكيمليك',
                        'phone_number': 'رقم الهاتف',
                        'birth_date': 'تاريخ الميلاد',
                        'address': 'العنوان',
                        'email': 'البريد الإلكتروني',
                        'password': 'كلمة المرور',
                        'confirm_password': 'تأكيد كلمة المرور'
                    }
                    return False, f"{field_names.get(field, field)} مطلوب"

            # التحقق من تطابق كلمات المرور
            if data['password'] != data['confirm_password']:
                return False, "كلمات المرور غير متطابقة"

            # التحقق من صحة البريد الإلكتروني
            is_valid, message = self.security.validate_email(data['email'])
            if not is_valid:
                return False, message

            # التحقق من رقم الكيمليك
            is_valid, message = self.security.validate_kimlik_number(data['kimlik_number'])
            if not is_valid:
                return False, message

            # التحقق من رقم الهاتف
            is_valid, message = self.security.validate_phone_number(data['phone_number'])
            if not is_valid:
                return False, message

            # التحقق من قوة كلمة المرور
            is_valid, message = self.security.validate_password_strength(data['password'])
            if not is_valid:
                return False, message

            return True, "البيانات صحيحة"

        except Exception as e:
            logger.error(f"خطأ في التحقق من بيانات التسجيل: {e}")
            return False, "حدث خطأ في التحقق من البيانات"

    def get_system_logs(self, limit: int = 100) -> list[Dict]:
        """
        الحصول على سجلات النظام (للمديرين فقط)

        Args:
            limit: عدد السجلات المطلوبة

        Returns:
            list[Dict]: قائمة السجلات
        """
        if not self.is_admin():
            return []

        try:
            result = self.db.execute_query('''
                SELECT sl.*, u.first_name, u.last_name, u.email
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.id
                ORDER BY sl.timestamp DESC
                LIMIT ?
            ''', (limit,))

            return [dict(row) for row in result]

        except Exception as e:
            logger.error(f"خطأ في الحصول على سجلات النظام: {e}")
            return []

# إنشاء مثيل مدير قاعدة البيانات للتطبيق
app_db = AppDatabaseManager()
