#!/usr/bin/env python3
"""
الأمان والتشفير - M<PERSON>rşid Security
إدارة تشفير كلمات المرور والأمان
"""

import hashlib
import secrets
import string
import re
from typing import Optional, Tuple
import logging
from datetime import datetime, timedelta

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityManager:
    """مدير الأمان والتشفير"""

    def __init__(self):
        """تهيئة مدير الأمان"""
        self.salt_length = 32
        self.min_password_length = 8
        self.max_login_attempts = 5
        self.lockout_duration = 30  # دقيقة

    def generate_salt(self) -> str:
        """
        إنشاء salt عشوائي للتشفير

        Returns:
            str: salt عشوائي
        """
        return secrets.token_hex(self.salt_length)

    def hash_password(self, password: str, salt: str = None) -> str:
        """
        تشفير كلمة المرور

        Args:
            password: كلمة المرور
            salt: المفتاح العشوائي (اختياري)

        Returns:
            str: كلمة المرور المشفرة مع المفتاح
        """
        if salt is None:
            salt = self.generate_salt()

        # دمج كلمة المرور مع المفتاح
        password_salt = f"{password}{salt}"

        # تشفير باستخدام SHA-256
        hashed = hashlib.sha256(password_salt.encode('utf-8')).hexdigest()

        # إرجاع المفتاح مع كلمة المرور المشفرة
        return f"{salt}:{hashed}"

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """
        التحقق من كلمة المرور

        Args:
            password: كلمة المرور المدخلة
            hashed_password: كلمة المرور المشفرة المحفوظة

        Returns:
            bool: True إذا كانت كلمة المرور صحيحة
        """
        try:
            # فصل المفتاح عن كلمة المرور المشفرة
            salt, stored_hash = hashed_password.split(':', 1)

            # تشفير كلمة المرور المدخلة بنفس المفتاح
            password_salt = f"{password}{salt}"
            input_hash = hashlib.sha256(password_salt.encode('utf-8')).hexdigest()

            # مقارنة آمنة
            return secrets.compare_digest(stored_hash, input_hash)

        except Exception as e:
            logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False

    def validate_password_strength(self, password: str) -> Tuple[bool, str]:
        """
        التحقق من قوة كلمة المرور

        Args:
            password: كلمة المرور

        Returns:
            Tuple[bool, str]: (صحيحة أم لا, رسالة الخطأ)
        """
        if len(password) < self.min_password_length:
            return False, f"كلمة المرور يجب أن تكون على الأقل {self.min_password_length} أحرف"

        if len(password) > 128:
            return False, "كلمة المرور طويلة جداً (الحد الأقصى 128 حرف)"

        # التحقق من وجود حرف كبير
        if not re.search(r'[A-Z]', password):
            return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"

        # التحقق من وجود حرف صغير
        if not re.search(r'[a-z]', password):
            return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"

        # التحقق من وجود رقم
        if not re.search(r'\d', password):
            return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"

        # التحقق من وجود رمز خاص
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"

        return True, "كلمة المرور قوية"

    def generate_secure_password(self, length: int = 12) -> str:
        """
        إنشاء كلمة مرور آمنة

        Args:
            length: طول كلمة المرور

        Returns:
            str: كلمة مرور آمنة
        """
        if length < self.min_password_length:
            length = self.min_password_length

        # تعريف مجموعات الأحرف
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*(),.?\":{}|<>"

        # ضمان وجود حرف من كل مجموعة
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]

        # إضافة باقي الأحرف عشوائياً
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))

        # خلط الأحرف
        secrets.SystemRandom().shuffle(password)

        return ''.join(password)

    def validate_email(self, email: str) -> Tuple[bool, str]:
        """
        التحقق من صحة البريد الإلكتروني

        Args:
            email: البريد الإلكتروني

        Returns:
            Tuple[bool, str]: (صحيح أم لا, رسالة الخطأ)
        """
        if not email:
            return False, "البريد الإلكتروني مطلوب"

        if len(email) > 254:
            return False, "البريد الإلكتروني طويل جداً"

        # نمط البريد الإلكتروني
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if not re.match(email_pattern, email):
            return False, "تنسيق البريد الإلكتروني غير صحيح"

        return True, "البريد الإلكتروني صحيح"

    def validate_kimlik_number(self, kimlik: str) -> Tuple[bool, str]:
        """
        التحقق من صحة رقم الكيمليك التركي

        Args:
            kimlik: رقم الكيمليك

        Returns:
            Tuple[bool, str]: (صحيح أم لا, رسالة الخطأ)
        """
        if not kimlik:
            return False, "رقم الكيمليك مطلوب"

        # إزالة المسافات والأحرف غير الرقمية
        kimlik = re.sub(r'\D', '', kimlik)

        if len(kimlik) != 11:
            return False, "رقم الكيمليك يجب أن يكون 11 رقم"

        if kimlik[0] == '0':
            return False, "رقم الكيمليك لا يمكن أن يبدأ بصفر"

        # للاختبار، سنقبل أرقام الكيمليك التي تبدأ بـ 1-9 وطولها 11 رقم
        # يمكن تفعيل الخوارزمية الكاملة لاحقاً
        try:
            # التحقق من أن جميع الأحرف أرقام
            int(kimlik)
            return True, "رقم الكيمليك صحيح"

        except ValueError:
            return False, "رقم الكيمليك يجب أن يحتوي على أرقام فقط"
        except Exception as e:
            logger.error(f"خطأ في التحقق من رقم الكيمليك: {e}")
            return False, "رقم الكيمليك غير صحيح"

    def validate_phone_number(self, phone: str) -> Tuple[bool, str]:
        """
        التحقق من صحة رقم الهاتف

        Args:
            phone: رقم الهاتف

        Returns:
            Tuple[bool, str]: (صحيح أم لا, رسالة الخطأ)
        """
        if not phone:
            return False, "رقم الهاتف مطلوب"

        # إزالة المسافات والأحرف غير الرقمية عدا + و -
        clean_phone = re.sub(r'[^\d+\-\s()]', '', phone)

        # نماذج أرقام الهاتف المقبولة
        patterns = [
            r'^\+90\s?5\d{2}\s?\d{3}\s?\d{2}\s?\d{2}$',  # +90 5XX XXX XX XX
            r'^0?5\d{2}\s?\d{3}\s?\d{2}\s?\d{2}$',       # 05XX XXX XX XX
            r'^\+90\s?\d{3}\s?\d{3}\s?\d{2}\s?\d{2}$',   # +90 XXX XXX XX XX
        ]

        for pattern in patterns:
            if re.match(pattern, clean_phone):
                return True, "رقم الهاتف صحيح"

        return False, "تنسيق رقم الهاتف غير صحيح"

    def generate_session_token(self) -> str:
        """
        إنشاء رمز جلسة آمن

        Returns:
            str: رمز الجلسة
        """
        return secrets.token_urlsafe(32)

    def is_safe_input(self, input_text: str) -> bool:
        """
        التحقق من أمان النص المدخل

        Args:
            input_text: النص المدخل

        Returns:
            bool: True إذا كان النص آمن
        """
        if not input_text:
            return True

        # البحث عن أنماط خطيرة
        dangerous_patterns = [
            r'<script',
            r'javascript:',
            r'on\w+\s*=',
            r'<iframe',
            r'<object',
            r'<embed',
            r'eval\s*\(',
            r'document\.',
            r'window\.',
        ]

        input_lower = input_text.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, input_lower):
                return False

        return True

# إنشاء مثيل مدير الأمان
security_manager = SecurityManager()
